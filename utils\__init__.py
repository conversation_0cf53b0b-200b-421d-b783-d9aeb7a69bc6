"""
وحدة الأدوات المساعدة للبرنامج
"""

import os

# التأكد من وجود مجلد utils
if not os.path.exists(os.path.dirname(__file__)):
    os.makedirs(os.path.dirname(__file__))

# دوال عرض الرسائل
def show_info_message(title, message):
    """عرض رسالة معلومات بدون تجميد الواجهة"""
    from PyQt5.QtWidgets import QMessageBox, QApplication
    from PyQt5.QtCore import QTimer

    try:
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setModal(False)  # غير مودال لتجنب التجميد
        msg_box.show()

        # إغلاق تلقائي بعد 3 ثوان
        QTimer.singleShot(3000, msg_box.close)

        # معالجة الأحداث لضمان عرض الرسالة
        QApplication.processEvents()

        return msg_box
    except Exception as e:
        print(f"خطأ في عرض رسالة المعلومات: {e}")

def show_error_message(title, message):
    """عرض رسالة خطأ بدون تجميد الواجهة"""
    from PyQt5.QtWidgets import QMessageBox, QApplication
    from PyQt5.QtCore import QTimer

    try:
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setModal(False)  # غير مودال لتجنب التجميد
        msg_box.show()

        # إغلاق تلقائي بعد 5 ثوان للأخطاء
        QTimer.singleShot(5000, msg_box.close)

        # معالجة الأحداث لضمان عرض الرسالة
        QApplication.processEvents()

        return msg_box
    except Exception as e:
        print(f"خطأ في عرض رسالة الخطأ: {e}")

def show_confirmation_message(title, message):
    """عرض رسالة تأكيد"""
    from PyQt5.QtWidgets import QMessageBox
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)
    return msg_box.exec_() == QMessageBox.Yes

# دوال مساعدة للجداول
def safe_get_id_from_table(table, row, column=0):
    """استخراج آمن للمعرف من الجدول"""
    try:
        if not table or row < 0 or row >= table.rowCount():
            return None

        item = table.item(row, column)
        if not item:
            return None

        id_text = item.text()
        if not id_text:
            return None

        # استخراج الأرقام فقط من النص
        numbers = ''.join(filter(str.isdigit, id_text))
        if numbers:
            return int(numbers)
        return None

    except Exception as e:
        print(f"خطأ في استخراج المعرف: {e}")
        return None

def safe_edit_item(parent, table, model_class, dialog_class, session, item_name="العنصر"):
    """دالة عامة آمنة لتعديل العناصر"""
    try:
        selected_row = table.currentRow()
        if selected_row < 0:
            # استخدام النوافذ المتطورة إذا كانت متاحة
            if hasattr(parent, 'show_warning_message'):
                parent.show_warning_message(f"يرجى اختيار {item_name} للتعديل")
            else:
                show_error_message("تحذير", f"يرجى اختيار {item_name} للتعديل")
            return False

        item_id = safe_get_id_from_table(table, selected_row)
        if not item_id:
            if hasattr(parent, 'show_error_message'):
                parent.show_error_message(f"لا يمكن تحديد {item_name} المحدد")
            else:
                show_error_message("خطأ", f"لا يمكن تحديد {item_name} المحدد")
            return False

        item = session.query(model_class).get(item_id)
        if not item:
            if hasattr(parent, 'show_error_message'):
                parent.show_error_message(f"لم يتم العثور على {item_name} في قاعدة البيانات")
            else:
                show_error_message("خطأ", f"لم يتم العثور على {item_name} في قاعدة البيانات")
            return False

        dialog = dialog_class(parent, item, session)
        if dialog.exec_() == dialog.Accepted:
            if hasattr(parent, 'show_success_message'):
                parent.show_success_message(f"تم تحديث {item_name} بنجاح")
            else:
                show_info_message("تم", f"تم تحديث {item_name} بنجاح")
            if hasattr(parent, 'refresh_data'):
                parent.refresh_data()
            return True

        return False

    except Exception as e:
        if hasattr(parent, 'show_error_message'):
            parent.show_error_message(f"حدث خطأ في تعديل {item_name}: {str(e)}")
        else:
            show_error_message("خطأ", f"حدث خطأ في تعديل {item_name}: {str(e)}")
        return False

def safe_delete_item(parent, table, model_class, session, item_name="العنصر", name_field="name"):
    """دالة عامة آمنة لحذف العناصر"""
    try:
        selected_row = table.currentRow()
        if selected_row < 0:
            show_error_message("تحذير", f"يرجى اختيار {item_name} للحذف")
            return False

        item_id = safe_get_id_from_table(table, selected_row)
        if not item_id:
            show_error_message("خطأ", f"لا يمكن تحديد {item_name} المحدد")
            return False

        item = session.query(model_class).get(item_id)
        if not item:
            show_error_message("خطأ", f"لم يتم العثور على {item_name} في قاعدة البيانات")
            return False

        # الحصول على اسم العنصر للعرض
        item_display_name = getattr(item, name_field, f"{item_name} رقم {item_id}")

        # طلب تأكيد الحذف
        if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف {item_name} '{item_display_name}'؟"):
            session.delete(item)
            session.commit()
            show_info_message("تم", f"تم حذف {item_name} بنجاح")
            if hasattr(parent, 'refresh_data'):
                parent.refresh_data()
            return True

        return False

    except Exception as e:
        session.rollback()
        show_error_message("خطأ", f"حدث خطأ في حذف {item_name}: {str(e)}")
        return False

# دوال التحقق من الإشعارات
def check_due_invoices(session):
    """التحقق من الفواتير المستحقة"""
    try:
        from database import Invoice, Notification
        import datetime
        today = datetime.datetime.now().date()
        due_invoices = session.query(Invoice).filter(
            Invoice.due_date <= today,
            Invoice.status.in_(['pending', 'partially_paid'])
        ).all()

        for invoice in due_invoices:
            # التحقق مما إذا كان هناك إشعار موجود بالفعل لهذه الفاتورة
            existing_notification = session.query(Notification).filter(
                Notification.type == 'invoice_due',
                Notification.related_id == invoice.id,
                Notification.is_read == False
            ).first()

            if not existing_notification:
                notification = Notification(
                    title="فاتورة مستحقة",
                    message=f"الفاتورة رقم {invoice.invoice_number} للعميل {invoice.client.name if invoice.client else 'غير محدد'} مستحقة الدفع.",
                    type='invoice_due',
                    related_id=invoice.id
                )
                session.add(notification)

        session.commit()
        return due_invoices
    except Exception as e:
        print(f"خطأ في التحقق من الفواتير المستحقة: {e}")
        return []

def check_due_supplier_payments(session):
    """التحقق من وجود مدفوعات مستحقة للموردين"""
    try:
        from database import Expense, Notification, Supplier
        import datetime
        today = datetime.datetime.now().date()

        # الحصول على جميع المصروفات غير المدفوعة أو المدفوعة جزئياً
        unpaid_expenses = session.query(Expense).filter(
            Expense.category.in_(['pending', 'partially_paid'])
        ).all()

        due_expenses = []

        for expense in unpaid_expenses:
            # استخراج تاريخ المبلغ المتبقي من الملاحظات
            if expense.notes and "|تاريخ المبلغ المتبقي:" in expense.notes:
                try:
                    due_date_str = expense.notes.split("|تاريخ المبلغ المتبقي:")[1].strip()
                    due_date = datetime.datetime.strptime(due_date_str, "%Y-%m-%d").date()

                    # التحقق مما إذا كان تاريخ المبلغ المتبقي قد حان أو تجاوز
                    if due_date <= today:
                        due_expenses.append(expense)

                        # التحقق مما إذا كان هناك إشعار موجود بالفعل لهذا المصروف
                        existing_notification = session.query(Notification).filter(
                            Notification.type == 'expense_due',
                            Notification.related_id == expense.id,
                            Notification.is_read == False
                        ).first()

                        if not existing_notification:
                            # الحصول على اسم المورد
                            supplier = session.query(Supplier).get(expense.supplier_id)
                            supplier_name = supplier.name if supplier else "غير معروف"

                            notification = Notification(
                                title="دفعة مستحقة",
                                message=f"الدفعة رقم {expense.title} للمورد {supplier_name} مستحقة الدفع.",
                                type='expense_due',
                                related_id=expense.id
                            )
                            session.add(notification)
                except (ValueError, IndexError) as e:
                    print(f"خطأ في استخراج تاريخ المبلغ المتبقي: {str(e)}")

        session.commit()
        return due_expenses
    except Exception as e:
        print(f"خطأ في التحقق من المدفوعات المستحقة للموردين: {str(e)}")
        return []

def check_upcoming_payments(session):
    """التحقق من وجود مدفوعات قادمة (قبل موعدها بأسبوع)"""
    try:
        from database import Invoice, Expense, Notification, Client, Supplier
        import datetime
        today = datetime.datetime.now().date()
        one_week_later = today + datetime.timedelta(days=7)

        # التحقق من الفواتير القادمة
        upcoming_invoices = []
        invoices = session.query(Invoice).filter(
            Invoice.due_date > today,
            Invoice.due_date <= one_week_later,
            Invoice.status.in_(['pending', 'partially_paid'])
        ).all()

        for invoice in invoices:
            upcoming_invoices.append(invoice)

            # التحقق مما إذا كان هناك إشعار موجود بالفعل لهذه الفاتورة
            existing_notification = session.query(Notification).filter(
                Notification.type == 'upcoming_invoice',
                Notification.related_id == invoice.id,
                Notification.is_read == False
            ).first()

            if not existing_notification:
                notification = Notification(
                    title="فاتورة قادمة",
                    message=f"الفاتورة رقم {invoice.invoice_number} للعميل {invoice.client.name if invoice.client else 'غير محدد'} مستحقة الدفع خلال أسبوع.",
                    type='upcoming_invoice',
                    related_id=invoice.id
                )
                session.add(notification)

        # التحقق من المصروفات القادمة
        upcoming_expenses = []
        unpaid_expenses = session.query(Expense).filter(
            Expense.category.in_(['pending', 'partially_paid'])
        ).all()

        for expense in unpaid_expenses:
            # استخراج تاريخ المبلغ المتبقي من الملاحظات
            if expense.notes and "|تاريخ المبلغ المتبقي:" in expense.notes:
                try:
                    due_date_str = expense.notes.split("|تاريخ المبلغ المتبقي:")[1].strip()
                    due_date = datetime.datetime.strptime(due_date_str, "%Y-%m-%d").date()

                    # التحقق مما إذا كان تاريخ المبلغ المتبقي خلال أسبوع
                    if today < due_date <= one_week_later:
                        upcoming_expenses.append(expense)

                        # التحقق مما إذا كان هناك إشعار موجود بالفعل لهذا المصروف
                        existing_notification = session.query(Notification).filter(
                            Notification.type == 'upcoming_expense',
                            Notification.related_id == expense.id,
                            Notification.is_read == False
                        ).first()

                        if not existing_notification:
                            # الحصول على اسم المورد
                            supplier = session.query(Supplier).get(expense.supplier_id)
                            supplier_name = supplier.name if supplier else "غير معروف"

                            notification = Notification(
                                title="دفعة قادمة",
                                message=f"الدفعة رقم {expense.title} للمورد {supplier_name} مستحقة الدفع خلال أسبوع.",
                                type='upcoming_expense',
                                related_id=expense.id
                            )
                            session.add(notification)
                except (ValueError, IndexError) as e:
                    # خطأ في استخراج تاريخ المبلغ المتبقي (تم إخفاء الرسالة)
                    pass

        session.commit()
        return upcoming_invoices, upcoming_expenses
    except Exception as e:
        # خطأ في التحقق من المدفوعات القادمة (تم إخفاء الرسالة)
        pass
        return [], []

def check_due_reminders(session):
    """التحقق من التنبيهات المستحقة"""
    try:
        from database import Reminder, Notification
        import datetime
        now = datetime.datetime.now()
        # الحصول على التنبيهات التي حان موعدها ولم تكتمل بعد
        due_reminders = session.query(Reminder).filter(
            Reminder.reminder_date <= now,
            Reminder.is_completed == False
        ).all()

        for reminder in due_reminders:
            # التحقق مما إذا كان هناك إشعار موجود بالفعل لهذا التنبيه
            existing_notification = session.query(Notification).filter(
                Notification.type == 'reminder_due',
                Notification.related_id == reminder.id,
                Notification.is_read == False
            ).first()

            if not existing_notification:
                # إنشاء إشعار جديد
                message = f"حان موعد التنبيه: {reminder.title}"

                notification = Notification(
                    title="تنبيه مستحق",
                    message=message,
                    type='reminder_due',
                    related_id=reminder.id
                )
                session.add(notification)

        session.commit()
        return due_reminders
    except Exception as e:
        # خطأ في التحقق من التنبيهات المستحقة (تم إخفاء الرسالة)
        print(f"خطأ في التحقق من التنبيهات المستحقة: {e}")
        return []

# دوال تنسيق العملة والكميات
def format_currency(amount, currency=None):
    """تنسيق المبلغ كعملة بدون كسور عشرية"""
    if currency is None:
        try:
            from database import get_setting, get_session
            session = get_session()
            currency = get_setting(session, 'currency', 'جنية')
        except:
            currency = 'جنية'

    # تقريب المبلغ إلى أقرب عدد صحيح
    amount_int = int(round(amount))

    # تنسيق المبلغ بدون كسور عشرية
    return f"{amount_int:,} {currency}"

def format_quantity(quantity):
    """تنسيق الكميات والأعداد بدون كسور عشرية"""
    # تقريب الكمية إلى أقرب عدد صحيح
    quantity_int = int(round(quantity))
    # تنسيق الكمية بدون كسور عشرية
    return f"{quantity_int}"

# دوال حساب الإيرادات والمصروفات
def calculate_total_revenue(session, start_date=None, end_date=None):
    """حساب إجمالي الإيرادات"""
    try:
        from database import Revenue
        query = session.query(Revenue)

        if start_date:
            query = query.filter(Revenue.date >= start_date)

        if end_date:
            query = query.filter(Revenue.date <= end_date)

        total = sum(revenue.amount for revenue in query.all())
        return total
    except Exception as e:
        # خطأ في حساب إجمالي الإيرادات (تم إخفاء الرسالة)
        pass
        return 0

def calculate_total_expenses(session, start_date=None, end_date=None):
    """حساب إجمالي المصروفات"""
    try:
        from database import Expense
        query = session.query(Expense)

        if start_date:
            query = query.filter(Expense.date >= start_date)

        if end_date:
            query = query.filter(Expense.date <= end_date)

        total = sum(expense.amount for expense in query.all())
        return total
    except Exception as e:
        # خطأ في حساب إجمالي المصروفات (تم إخفاء الرسالة)
        pass
        return 0

def calculate_net_profit(session, start_date=None, end_date=None):
    """حساب صافي الربح"""
    total_revenue = calculate_total_revenue(session, start_date, end_date)
    total_expenses = calculate_total_expenses(session, start_date, end_date)
    return total_revenue - total_expenses

# دوال التحقق من صحة البيانات
def is_valid_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email)) if email else True

def is_valid_phone(phone):
    """التحقق من صحة رقم الهاتف"""
    import re
    pattern = r'^[0-9+\-\s()]{8,15}$'
    return bool(re.match(pattern, phone)) if phone else True

# دوال تحويل التواريخ
def qdate_to_datetime(qdate):
    """تحويل QDate إلى datetime"""
    import datetime
    if qdate and hasattr(qdate, 'year') and hasattr(qdate, 'month') and hasattr(qdate, 'day'):
        try:
            return datetime.datetime(qdate.year(), qdate.month(), qdate.day())
        except Exception as e:
            # خطأ في تحويل QDate إلى datetime (تم إخفاء الرسالة)
            pass
            # في حالة حدوث خطأ، إرجاع التاريخ الحالي
            return datetime.datetime.now()
    return datetime.datetime.now()

def datetime_to_qdate(dt):
    """تحويل datetime إلى QDate"""
    from PyQt5.QtCore import QDate
    if dt and hasattr(dt, 'year') and hasattr(dt, 'month') and hasattr(dt, 'day'):
        try:
            return QDate(dt.year, dt.month, dt.day)
        except Exception as e:
            # خطأ في تحويل datetime إلى QDate (تم إخفاء الرسالة)
            pass
            # في حالة حدوث خطأ، إرجاع التاريخ الحالي
            return QDate.currentDate()
    return QDate.currentDate()

def format_datetime(dt):
    """تنسيق التاريخ والوقت بنظام 12 ساعة مع فاصل بينهما"""
    if not dt:
        return ""

    # تنسيق التاريخ
    date_str = dt.strftime("%Y-%m-%d")

    # تنسيق الوقت بنظام 12 ساعة
    hour = dt.hour
    am_pm = "ص" if hour < 12 else "م"

    # تحويل الساعة إلى نظام 12 ساعة
    if hour == 0:
        hour = 12
    elif hour > 12:
        hour = hour - 12

    time_str = f"{hour:02d}:{dt.minute:02d} {am_pm}"

    # دمج التاريخ والوقت مع فاصل بينهما
    return f"{date_str} | {time_str}"

def format_date(date):
    """وظيفة لتنسيق التاريخ بشكل مناسب للغة العربية"""
    import datetime

    if not date:
        return "غير متوفر"

    # تحويل التاريخ إلى نص بتنسيق مناسب للغة العربية
    if isinstance(date, datetime.date) or isinstance(date, datetime.datetime):
        # تنسيق التاريخ: اليوم/الشهر/السنة
        return date.strftime("%d/%m/%Y")
    elif isinstance(date, str):
        # محاولة تحويل النص إلى تاريخ
        try:
            date_obj = datetime.datetime.strptime(date, "%Y-%m-%d")
            return date_obj.strftime("%d/%m/%Y")
        except ValueError:
            return date
    else:
        return str(date)

# دوال الفواتير
def generate_invoice_number(session):
    """توليد رقم فاتورة جديد"""
    from database import Invoice
    import datetime

    # الحصول على السنة الحالية
    current_year = datetime.datetime.now().year

    # البحث عن آخر فاتورة في السنة الحالية
    last_invoice = session.query(Invoice).filter(
        Invoice.invoice_number.like(f"INV-{current_year}-%")
    ).order_by(Invoice.invoice_number.desc()).first()

    if last_invoice:
        # استخراج الرقم التسلسلي من رقم الفاتورة
        try:
            last_number = int(last_invoice.invoice_number.split('-')[-1])
            new_number = last_number + 1
        except:
            new_number = 1
    else:
        new_number = 1

    # توليد رقم الفاتورة الجديد
    return f"INV-{current_year}-{new_number:04d}"

# دوال الأقساط
def generate_installment_number(session):
    """توليد رقم قسط جديد"""
    from database import Installment
    import datetime

    # الحصول على السنة الحالية
    current_year = datetime.datetime.now().year

    # البحث عن آخر قسط في السنة الحالية
    last_installment = session.query(Installment).filter(
        Installment.installment_number.like(f"INST-{current_year}-%")
    ).order_by(Installment.installment_number.desc()).first()

    if last_installment:
        # استخراج الرقم التسلسلي من رقم القسط
        try:
            last_number = int(last_installment.installment_number.split('-')[-1])
            new_number = last_number + 1
        except:
            new_number = 1
    else:
        new_number = 1

    # توليد رقم القسط الجديد
    return f"INST-{current_year}-{new_number:04d}"

def calculate_invoice_balance(invoice):
    """حساب الرصيد المتبقي للفاتورة"""
    if not invoice:
        return 0

    # حساب إجمالي الفاتورة
    total = invoice.total_amount

    # حساب إجمالي المدفوعات
    payments = sum(payment.amount for payment in invoice.payments)

    # حساب الرصيد المتبقي
    return total - payments

# دوال الربط الآمن للأحداث - حل نهائي لمشكلة الدوال المفقودة
def safe_connect_action(action, obj, method_name, fallback_message="الميزة قيد التطوير"):
    """
    ربط آمن للأحداث مع فحص وجود الدالة
    يحل مشكلة الأخطاء عند إضافة دوال جديدة للكلاسات

    Args:
        action: QAction المراد ربطه
        obj: الكائن الذي يحتوي على الدالة
        method_name: اسم الدالة كنص
        fallback_message: الرسالة البديلة إذا لم توجد الدالة

    Returns:
        bool: True إذا تم الربط بنجاح، False إذا تم استخدام الرسالة البديلة
    """
    try:
        if hasattr(obj, method_name):
            method = getattr(obj, method_name)
            if callable(method):
                action.triggered.connect(method)
                return True

        # إذا لم توجد الدالة، ربط رسالة بديلة
        action.triggered.connect(lambda: show_info_message("قريباً", fallback_message))
        return False

    except Exception as e:
        print(f"خطأ في ربط الحدث {method_name}: {e}")
        action.triggered.connect(lambda: show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}"))
        return False

def create_safe_export_menu(parent_obj, menu_items):
    """
    إنشاء قائمة تصدير آمنة مع فحص وجود جميع الدوال

    Args:
        parent_obj: الكائن الأب الذي يحتوي على الدوال
        menu_items: قائمة بالعناصر في شكل (text, method_name, fallback_message) أو "separator"

    Returns:
        QMenu: القائمة المنسدلة الآمنة
    """
    from PyQt5.QtWidgets import QMenu, QAction

    menu = QMenu(parent_obj)

    # تطبيق التصميم الموحد
    menu.setStyleSheet("""
        QMenu {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                stop:0.9 #6D28D9, stop:1 #5B21B6);
            border: none;
            border-radius: 12px;
            padding: 8px;
            color: #ffffff;
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            font-weight: 900;
            font-size: 13px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                       0 5px 15px rgba(59, 130, 246, 0.2),
                       inset 0 1px 0 rgba(255, 255, 255, 0.1);
            min-width: 200px;
        }
        QMenu::item {
            background: transparent;
            padding: 10px 15px;
            margin: 2px;
            border: none;
            border-radius: 8px;
            color: #ffffff;
            font-weight: 700;
            font-size: 13px;
            text-align: center;
            min-height: 20px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }
        QMenu::item:selected {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(59, 130, 246, 0.8),
                stop:0.3 rgba(96, 165, 250, 0.7),
                stop:0.7 rgba(139, 92, 246, 0.6),
                stop:1 rgba(124, 58, 237, 0.7));
            color: #ffffff;
            font-weight: 900;
            border: 1px solid rgba(255, 255, 255, 0.4);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                       0 0 15px rgba(96, 165, 250, 0.3),
                       inset 0 1px 0 rgba(255, 255, 255, 0.2);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            transform: scale(1.02);
        }
        QMenu::item:hover {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 rgba(96, 165, 250, 0.3),
                stop:0.5 rgba(139, 92, 246, 0.25),
                stop:1 rgba(124, 58, 237, 0.3));
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
        }
        QMenu::separator {
            height: 2px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 rgba(255, 255, 255, 0.2),
                stop:0.5 rgba(96, 165, 250, 0.4),
                stop:1 rgba(255, 255, 255, 0.2));
            margin: 6px 12px;
            border: none;
            border-radius: 1px;
        }
    """)

    for item in menu_items:
        if item == "separator":
            menu.addSeparator()
            continue

        text, method_name, fallback_message = item
        action = QAction(text, parent_obj)
        safe_connect_action(action, parent_obj, method_name, fallback_message)
        menu.addAction(action)

    return menu

def get_standard_export_menu_items():
    """
    الحصول على عناصر قائمة التصدير الموحدة لجميع الأقسام

    Returns:
        list: قائمة بعناصر القائمة الموحدة
    """
    return [
        # قسم التصدير الأساسي
        ("📊 تصدير Excel متقدم", "export_excel_advanced", "ميزة تصدير Excel المتقدم قيد التطوير"),
        ("📄 تصدير CSV شامل", "export_csv_advanced", "ميزة تصدير CSV الشامل قيد التطوير"),
        ("📋 تصدير PDF تفصيلي", "export_pdf_advanced", "ميزة تصدير PDF التفصيلي قيد التطوير"),

        "separator",

        # قسم التقارير المتقدمة (يختلف حسب القسم)
        ("📊 تقرير متقدم", "export_advanced_report", "ميزة التقرير المتقدم قيد التطوير"),
        ("💰 تحليل مالي", "export_financial_analysis", "ميزة التحليل المالي قيد التطوير"),
        ("📅 التقرير الشهري", "export_monthly_report", "ميزة التقرير الشهري قيد التطوير"),

        "separator",

        # قسم التصدير المخصص
        ("⚙️ تصدير مخصص", "export_custom", "ميزة التصدير المخصص قيد التطوير"),
        ("💾 إنشاء نسخة احتياطية", "export_backup", "ميزة النسخ الاحتياطي قيد التطوير"),
        ("📥 استعادة نسخة احتياطية", "restore_backup", "ميزة استعادة النسخ الاحتياطية قيد التطوير")
    ]
