from PyQt5.QtWidgets import (<PERSON>Widget, Q<PERSON>oxLayout, QHBox<PERSON>ayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame,
                            QTextBrowser)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QPainter, QTextDocument, QPixmap, QBrush, QPen, QIcon, QLinearGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Invoice, InvoiceItem, Client
# from utils import (show_error_message, show_info_message, show_confirmation_message,  # لم تعد مستخدمة
from utils import (qdate_to_datetime, datetime_to_qdate, format_currency,
                    generate_invoice_number, format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func

# ═══════════════════════════════════════════════════════════════════════════════════
# النوافذ المتطورة للفواتير مطابقة لنافذة الحذف المرجعية
# ═══════════════════════════════════════════════════════════════════════════════════

class InvoiceWarningDialog(QDialog):
    """نافذة تحذير متطورة للفواتير مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="تحذير", message="", icon="⚠️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الفواتير المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(245, 158, 11, 0.2),
                    stop:0.5 rgba(217, 119, 6, 0.3),
                    stop:1 rgba(180, 83, 9, 0.2));
                border: 2px solid rgba(245, 158, 11, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'warning')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(245, 158, 11))
            gradient.setColorAt(0.7, QColor(217, 119, 6))
            gradient.setColorAt(1, QColor(180, 83, 9))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 14, QFont.Bold))
            painter.drawText(14, 30, "⚠️")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InvoiceErrorDialog(QDialog):
    """نافذة خطأ متطورة للفواتير مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="خطأ", message="", icon="❌"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الفواتير المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'danger')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 14, QFont.Bold))
            painter.drawText(14, 30, "❌")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class InvoiceInfoDialog(QDialog):
    """نافذة معلومات متطورة للفواتير مطابقة لنافذة الحذف"""

    def __init__(self, parent=None, title="معلومات", message="", icon="ℹ️"):
        super().__init__(parent)
        self.parent_widget = parent
        self.setup_ui(title, message, icon)

    def setup_ui(self, title, message, icon):
        """إعداد واجهة النافذة مطابقة لنافذة الحذف"""
        self.setWindowTitle(f"{icon} {title} - نظام إدارة الفواتير المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel(f"{icon} {title}")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.2),
                    stop:0.5 rgba(37, 99, 235, 0.3),
                    stop:1 rgba(29, 78, 216, 0.2));
                border: 2px solid rgba(59, 130, 246, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # رسالة المحتوى
        message_label = QLabel(message)
        message_label.setAlignment(Qt.AlignCenter)
        message_label.setWordWrap(True)
        message_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 12px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:0.5 rgba(248, 250, 252, 0.15),
                    stop:1 rgba(241, 245, 249, 0.1));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 15px;
                margin: 3px 0;
                text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(message_label)

        # زر الإغلاق
        close_button = QPushButton("✅ حسناً")
        self.style_advanced_button(close_button, 'info')
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'warning': '#F59E0B',
                    'danger': '#EF4444',
                    'info': '#3B82F6',
                    'success': '#10B981'
                }
                color = colors.get(button_type, '#6B7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from PyQt5.QtGui import QPixmap, QRadialGradient, QBrush, QPen, QPainter, QIcon, QColor, QFont
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(59, 130, 246))
            gradient.setColorAt(0.7, QColor(37, 99, 235))
            gradient.setColorAt(1, QColor(29, 78, 216))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 14, QFont.Bold))
            painter.drawText(14, 30, "ℹ️")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        try:
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم شريط العنوان: {e}")


class DeleteInvoiceDialog(QDialog):
    """نافذة حذف الفاتورة مشابهة لنافذة حذف العميل"""

    def __init__(self, parent=None, invoice=None):
        super().__init__(parent)
        self.invoice = invoice
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة حذف العميل"""
        self.setWindowTitle("📄 حذف - نظام إدارة الفواتير المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.customize_title_bar()
        self.setModal(True)
        self.resize(400, 250)

        # خلفية النافذة مطابقة لنوافذ البرنامج
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(8)

        # عنوان النافذة الداخلي
        title_label = QLabel("📄 حذف الفاتورة")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(239, 68, 68, 0.2),
                    stop:0.5 rgba(220, 38, 38, 0.3),
                    stop:1 rgba(185, 28, 28, 0.2));
                border: 2px solid rgba(239, 68, 68, 0.5);
                border-radius: 8px;
                padding: 6px;
                margin: 3px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(title_label)

        # معلومات الفاتورة مضغوطة
        if self.invoice:
            info_text = f"📄 {self.invoice.invoice_number[:15]}{'...' if len(self.invoice.invoice_number) > 15 else ''}"
            if self.invoice.total_amount:
                info_text += f" | 💰 {self.invoice.total_amount:.0f} ج"

            info_label = QLabel(info_text)
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: bold;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.1),
                        stop:0.5 rgba(248, 250, 252, 0.15),
                        stop:1 rgba(241, 245, 249, 0.1));
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    border-radius: 6px;
                    padding: 6px;
                    margin: 3px 0;
                    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
                }
            """)
            layout.addWidget(info_label)

        # سؤال التأكيد
        question_label = QLabel("⚠️ متأكد من الحذف؟")
        question_label.setAlignment(Qt.AlignCenter)
        question_label.setStyleSheet("""
            QLabel {
                color: #fbbf24;
                font-size: 14px;
                font-weight: bold;
                margin: 6px 0;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
        """)
        layout.addWidget(question_label)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        cancel_button = QPushButton("❌ إلغاء")
        self.style_advanced_button(cancel_button, 'info')
        cancel_button.clicked.connect(self.reject)

        confirm_button = QPushButton("📄 حذف")
        self.style_advanced_button(confirm_button, 'danger')
        confirm_button.clicked.connect(self.accept)

        buttons_layout.addWidget(cancel_button)
        buttons_layout.addWidget(confirm_button)
        layout.addLayout(buttons_layout)

    def style_advanced_button(self, button, button_type):
        """تطبيق تصميم متطور على الأزرار"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type)
            else:
                colors = {
                    'danger': '#ef4444',
                    'info': '#3b82f6'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 12px 30px;
                        font-size: 16px;
                        font-weight: bold;
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                        transform: translateY(-2px);
                    }}
                    QPushButton:pressed {{
                        background-color: {color}bb;
                        transform: translateY(0px);
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            from PyQt5.QtGui import QRadialGradient
            gradient = QRadialGradient(24, 24, 20)
            gradient.setColorAt(0, QColor(239, 68, 68))
            gradient.setColorAt(0.7, QColor(220, 38, 38))
            gradient.setColorAt(1, QColor(185, 28, 28))

            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📄")
            painter.end()

            self.setWindowIcon(QIcon(pixmap))
            self.apply_advanced_title_bar_styling()
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)


# ═══════════════════════════════════════════════════════════════════════════════════
# دوال مساعدة لعرض النوافذ المتطورة للفواتير
# ═══════════════════════════════════════════════════════════════════════════════════

def ensure_invoice_dialog_stays_visible(dialog):
    """التأكد من أن النافذة تبقى مرئية"""
    if dialog:
        dialog.setModal(True)
        dialog.raise_()
        dialog.activateWindow()
        dialog.show()
        return dialog

def show_invoice_advanced_warning(parent, title, message, icon="⚠️"):
    """عرض نافذة تحذير متطورة للفواتير"""
    dialog = InvoiceWarningDialog(parent, title, message, icon)
    return ensure_invoice_dialog_stays_visible(dialog).exec_()

def show_invoice_advanced_error(parent, title, message, icon="❌"):
    """عرض نافذة خطأ متطورة للفواتير"""
    dialog = InvoiceErrorDialog(parent, title, message, icon)
    return ensure_invoice_dialog_stays_visible(dialog).exec_()

def show_invoice_advanced_info(parent, title, message, icon="ℹ️"):
    """عرض نافذة معلومات متطورة للفواتير"""
    dialog = InvoiceInfoDialog(parent, title, message, icon)
    return ensure_invoice_dialog_stays_visible(dialog).exec_()


class InvoiceItemDialog(QDialog):
    """نافذة حوار لإضافة أو تعديل عنصر فاتورة"""

    def __init__(self, parent=None, item=None):
        super().__init__(parent)
        self.item = item
        self.init_ui()

    def init_ui(self):
        # إعداد نافذة الحوار
        if self.item:
            self.setWindowTitle("تعديل عنصر الفاتورة")
        else:
            self.setWindowTitle("إضافة عنصر جديد للفاتورة")

        self.setMinimumWidth(400)

        # إنشاء النموذج
        form_layout = QFormLayout()

        # حقل الوصف
        self.description_edit = QLineEdit()
        self.description_edit.setStyleSheet(UnifiedStyles.get_input_style())
        if self.item:
            self.description_edit.setText(self.item.description)
        form_layout.addRow("الوصف:", self.description_edit)

        # حقل الكمية
        self.quantity_edit = QDoubleSpinBox()
        self.quantity_edit.setRange(1, 10000)  # الحد الأدنى 1 بدلاً من 0.01
        self.quantity_edit.setDecimals(0)  # بدون كسور عشرية
        self.quantity_edit.setSingleStep(1)
        self.quantity_edit.setValue(1)
        if self.item:
            self.quantity_edit.setValue(self.item.quantity)
        self.quantity_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_edit)

        # حقل سعر الوحدة
        self.unit_price_edit = QDoubleSpinBox()
        self.unit_price_edit.setRange(1, 1000000)  # الحد الأدنى 1 بدلاً من 0.01
        self.unit_price_edit.setDecimals(0)  # بدون كسور عشرية
        self.unit_price_edit.setSingleStep(10)
        if self.item:
            self.unit_price_edit.setValue(self.item.unit_price)
        self.unit_price_edit.valueChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_edit)

        # حقل السعر الإجمالي
        self.total_price_label = QLabel("0.00")
        form_layout.addRow("السعر الإجمالي:", self.total_price_label)

        # حساب السعر الإجمالي الأولي
        self.calculate_total()

        # أزرار التحكم - مطابقة للعملاء والموردين (ترتيب صحيح)
        button_layout = QHBoxLayout()

        # زر الحفظ مطابق للعملاء والموردين
        save_button = QPushButton("💾 حفظ")
        if hasattr(self.parent(), 'style_advanced_button'):
            self.parent().style_advanced_button(save_button, 'emerald')
        save_button.clicked.connect(self.accept)

        # زر الإلغاء مطابق للعملاء والموردين
        cancel_button = QPushButton("❌ إلغاء")
        if hasattr(self.parent(), 'style_advanced_button'):
            self.parent().style_advanced_button(cancel_button, 'danger')
        cancel_button.clicked.connect(self.reject)

        # ترتيب صحيح: الإلغاء أولاً ثم الحفظ
        button_layout.addWidget(cancel_button)
        button_layout.addWidget(save_button)

        # تجميع التخطيط النهائي
        main_layout = QVBoxLayout()
        main_layout.addLayout(form_layout)
        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def calculate_total(self):
        """حساب السعر الإجمالي للعنصر"""
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price
        self.total_price_label.setText(format_currency(total_price))

    def get_data(self):
        """الحصول على بيانات عنصر الفاتورة من النموذج"""
        description = self.description_edit.text().strip()
        quantity = self.quantity_edit.value()
        unit_price = self.unit_price_edit.value()
        total_price = quantity * unit_price

        # التحقق من صحة البيانات
        if not description:
            show_invoice_advanced_error(self, "خطأ", "يجب إدخال وصف العنصر")
            return None

        if quantity <= 0:
            show_invoice_advanced_error(self, "خطأ", "يجب أن تكون الكمية أكبر من صفر")
            return None

        if unit_price <= 0:
            show_invoice_advanced_error(self, "خطأ", "يجب أن يكون سعر الوحدة أكبر من صفر")
            return None

        return {
            'description': description,
            'quantity': quantity,
            'unit_price': unit_price,
            'total_price': total_price
        }


class InvoiceDialog(QDialog):
    """نافذة إضافة/تعديل فاتورة - تصميم جديد نظيف"""

    def __init__(self, parent=None, invoice=None, session=None):
        super().__init__(parent)
        self.invoice = invoice
        self.session = session
        self.items = []

        # نسخ عناصر الفاتورة الموجودة
        if self.invoice and self.invoice.items:
            for item in self.invoice.items:
                self.items.append({
                    'id': item.id,
                    'description': item.description,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                })

        self.setup_ui()
        self.update_total()

    def setup_ui(self):
        """إعداد واجهة النافذة"""
        # إعداد النافذة مطابق للعملاء والموردين
        title = "تعديل فاتورة" if self.invoice else "إضافة فاتورة جديدة"
        self.setWindowTitle(f"📄 {title} - نظام إدارة الفواتير المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان
        self.customize_title_bar()

        self.setModal(True)
        self.resize(750, 480)  # تقليل الارتفاع بعد إزالة العنوان

        # خلفية النافذة الرئيسية مطابقة تماماً للعملاء والموردين
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للعملاء والموردين"""
        try:
            # إنشاء أيقونة مخصصة للفواتير
            pixmap = QPixmap(48, 48)
            pixmap.fill(Qt.transparent)

            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)

            # تدرج مطابق للعملاء والموردين
            gradient = QLinearGradient(0, 0, 48, 48)
            gradient.setColorAt(0, QColor(59, 130, 246))  # أزرق
            gradient.setColorAt(0.5, QColor(147, 51, 234))  # بنفسجي
            gradient.setColorAt(1, QColor(236, 72, 153))  # وردي

            # رسم دائرة متدرجة
            painter.setBrush(QBrush(gradient))
            painter.setPen(QPen(QColor(255, 255, 255, 180), 2))
            painter.drawEllipse(4, 4, 40, 40)

            # رسم رمز الفواتير
            painter.setPen(QPen(QColor(255, 255, 255), 3))
            painter.setFont(QFont("Arial", 16, QFont.Bold))
            painter.drawText(12, 30, "📄")

            painter.end()

            # تعيين الأيقونة
            icon = QIcon(pixmap)
            self.setWindowIcon(icon)

            # تطبيق تصميم متطور على شريط العنوان
            self.apply_advanced_title_bar_styling()

        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان"""
        TitleBarStyler.apply_advanced_title_bar_styling(self)

        # تخطيط المحتوى الرئيسي للنافذة مع مسافات مقللة
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات بين العناصر

        # نموذج البيانات الأساسية مع مسافات مقللة
        form_widget = QWidget()
        form_layout = QFormLayout(form_widget)
        form_layout.setSpacing(10)
        form_layout.setContentsMargins(10, 10, 10, 10)

        # دالة لإنشاء تسميات مصممة مضغوطة مع خلفية قوية
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setProperty("class", "styled-title")  # إضافة فئة للتمييز
            label.setStyleSheet("""
                QLabel[class="styled-title"] {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.95),
                        stop:0.3 rgba(96, 165, 250, 0.9),
                        stop:0.7 rgba(139, 92, 246, 0.9),
                        stop:1 rgba(124, 58, 237, 0.95)) !important;
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 6px 10px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 100px;
                    max-width: 100px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)
        else:
            self.invoice_number_edit.setText(generate_invoice_number(self.session))
        self.style_input(self.invoice_number_edit)
        form_layout.addRow(create_styled_label("رقم الفاتورة", "📄", True), self.invoice_number_edit)

        # العميل
        self.client_combo = QComboBox()
        self.client_combo.addItem("-- اختر عميل --", None)
        if self.session:
            clients = self.session.query(Client).all()
            for client in clients:
                self.client_combo.addItem(client.name, client.id)

        if self.invoice and self.invoice.client_id:
            index = self.client_combo.findData(self.invoice.client_id)
            if index >= 0:
                self.client_combo.setCurrentIndex(index)

        self.style_combo(self.client_combo)
        form_layout.addRow(create_styled_label("العميل", "👤", True), self.client_combo)

        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        if self.invoice and self.invoice.date:
            self.date_edit.setDate(datetime_to_qdate(self.invoice.date))
        self.style_input(self.date_edit)
        form_layout.addRow(create_styled_label("التاريخ", "📅", True), self.date_edit)

        # تاريخ الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(datetime_to_qdate(self.invoice.due_date))
        self.style_input(self.due_date_edit)
        form_layout.addRow(create_styled_label("تاريخ الاستحقاق", "⏰"), self.due_date_edit)

        # الحالة
        self.status_combo = QComboBox()
        statuses = [("pending", "معلقة"), ("paid", "مدفوعة"), ("partially_paid", "مدفوعة جزئياً"), ("cancelled", "ملغية")]
        for status_code, status_name in statuses:
            self.status_combo.addItem(status_name, status_code)

        if self.invoice and self.invoice.status:
            index = self.status_combo.findData(self.invoice.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

        self.style_combo(self.status_combo)
        form_layout.addRow(create_styled_label("الحالة", "📊"), self.status_combo)

        # المبلغ المدفوع
        self.paid_amount_edit = QDoubleSpinBox()
        self.paid_amount_edit.setRange(0, 1000000)
        self.paid_amount_edit.setDecimals(2)
        self.paid_amount_edit.setSingleStep(100)
        if self.invoice:
            self.paid_amount_edit.setValue(self.invoice.paid_amount)
        self.style_input(self.paid_amount_edit)
        form_layout.addRow(create_styled_label("المبلغ المدفوع", "💰"), self.paid_amount_edit)

        layout.addWidget(form_widget)

        # جدول العناصر مع مسافات مقللة
        items_widget = QWidget()
        items_layout = QVBoxLayout(items_widget)
        items_layout.setSpacing(8)
        items_layout.setContentsMargins(5, 5, 5, 5)

        # عنوان العناصر مضغوط مع خلفية قوية
        items_title = QLabel("📋 عناصر الفاتورة")
        items_title.setProperty("class", "styled-title")
        items_title.setStyleSheet("""
            QLabel[class="styled-title"] {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.95),
                    stop:0.3 rgba(96, 165, 250, 0.9),
                    stop:0.7 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(124, 58, 237, 0.95)) !important;
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 6px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                margin: 5px 0;
            }
        """)
        items_layout.addWidget(items_title)

        # الجدول مضغوط
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["الوصف", "الكمية", "سعر الوحدة", "الإجمالي", "حذف"])

        # تصميم الجدول مطابق للعملاء والموردين
        self.items_table.setStyleSheet("""
            QTableWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.5 rgba(248, 250, 252, 0.9),
                    stop:1 rgba(241, 245, 249, 0.85));
                border: 1px solid rgba(96, 165, 250, 0.3);
                border-radius: 6px;
                gridline-color: rgba(96, 165, 250, 0.2);
                selection-background-color: rgba(96, 165, 250, 0.3);
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 8px 12px !important;
                border: 2px solid rgba(255, 255, 255, 0.3) !important;
                border-radius: 8px !important;
                font-weight: bold !important;
                font-size: 12px !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7) !important;
                text-align: center !important;
                height: 35px !important;
                letter-spacing: 1px !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-1px) scale(1.01) !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 15px rgba(134, 158, 234, 0.3) !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.99) !important;
                box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.5) !important;
            }
        """)

        # ضبط ارتفاع الصفوف مقلل
        self.items_table.verticalHeader().setDefaultSectionSize(30)
        self.items_table.setMaximumHeight(150)  # تقليل ارتفاع الجدول أكثر

        self.items_table.horizontalHeader().setStretchLastSection(True)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        items_layout.addWidget(self.items_table)

        # أزرار العناصر مطابقة للنمط الموحد
        items_buttons = QHBoxLayout()
        items_buttons.setSpacing(15)

        add_item_btn = QPushButton("➕ إضافة عنصر")
        add_item_btn.clicked.connect(self.add_item)
        self.style_button(add_item_btn, "emerald")
        items_buttons.addWidget(add_item_btn)

        edit_item_btn = QPushButton("✏️ تعديل عنصر")
        edit_item_btn.clicked.connect(self.edit_item)
        self.style_button(edit_item_btn, "info")
        items_buttons.addWidget(edit_item_btn)

        delete_item_btn = QPushButton("🗑️ حذف عنصر")
        delete_item_btn.clicked.connect(self.delete_selected_item)
        self.style_button(delete_item_btn, "danger")
        items_buttons.addWidget(delete_item_btn)

        # إضافة مساحة مرنة
        items_buttons.addStretch()

        items_layout.addLayout(items_buttons)

        # إجمالي الفاتورة مطابق للنمط الموحد مع خلفية قوية
        self.total_label = QLabel("الإجمالي: 0.00 جنيه")
        self.total_label.setProperty("class", "styled-title")
        self.total_label.setStyleSheet("""
            QLabel[class="styled-title"] {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(16, 185, 129, 0.95),
                    stop:0.3 rgba(34, 197, 94, 0.9),
                    stop:0.7 rgba(59, 130, 246, 0.9),
                    stop:1 rgba(139, 92, 246, 0.95)) !important;
                border: 2px solid rgba(16, 185, 129, 0.9);
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 18px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(16, 185, 129, 0.4);
                margin: 10px 0;
            }
        """)
        items_layout.addWidget(self.total_label)

        layout.addWidget(items_widget)

        # الملاحظات مع مسافات مقللة
        notes_widget = QWidget()
        notes_layout = QVBoxLayout(notes_widget)
        notes_layout.setSpacing(6)
        notes_layout.setContentsMargins(5, 5, 5, 5)

        notes_title = QLabel("📝 ملاحظات")
        notes_title.setProperty("class", "styled-title")
        notes_title.setStyleSheet("""
            QLabel[class="styled-title"] {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.95),
                    stop:0.3 rgba(96, 165, 250, 0.9),
                    stop:0.7 rgba(139, 92, 246, 0.9),
                    stop:1 rgba(124, 58, 237, 0.95)) !important;
                border: 2px solid rgba(96, 165, 250, 0.9);
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                margin: 5px 0;
            }
        """)
        notes_layout.addWidget(notes_title)

        self.notes_edit = QTextEdit()
        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)
        # تصميم مضغوط للملاحظات
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: normal;
                color: #1f2937;
                min-height: 40px;
                max-height: 60px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
            }
            QTextEdit:focus {
                border: 3px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
            }
        """)
        notes_layout.addWidget(self.notes_edit)

        layout.addWidget(notes_widget)

        # أزرار التحكم مع مسافات مقللة
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(10, 8, 10, 8)

        # إضافة مساحة مرنة في البداية
        buttons_layout.addStretch()

        # زر الإلغاء - أحمر للخطر مطابق للعملاء والموردين
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.clicked.connect(self.reject)
        self.style_button(cancel_btn, "danger")
        buttons_layout.addWidget(cancel_btn)

        # زر الحفظ مطابق للعملاء والموردين
        save_btn = QPushButton("💾 حفظ")
        save_btn.clicked.connect(self.accept)
        self.style_button(save_btn, "emerald")
        buttons_layout.addWidget(save_btn)

        # إضافة مساحة مرنة في النهاية
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # تحديث الجدول
        self.update_items_table()

    def style_input(self, widget):
        """تطبيق تصميم مضغوط على حقول الإدخال"""
        widget.setStyleSheet("""
            QLineEdit, QDateEdit, QDoubleSpinBox, QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: normal;
                color: #1f2937;
                min-height: 20px;
                min-width: 280px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.2);
            }
            QLineEdit:focus, QDateEdit:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border: 3px solid rgba(96, 165, 250, 0.95);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.4);
            }
        """)

    def style_combo(self, combo):
        """تطبيق تصميم موحد على القوائم المنسدلة مطابق للنمط الجديد (بدون إطارات)"""
        combo.setStyleSheet("""
            QComboBox {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: none;
                border-radius: 10px;
                padding: 14px 18px;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 4px 15px rgba(96, 165, 250, 0.25);
            }
            QComboBox:hover {
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(96, 165, 250, 0.3);
            }
            QComboBox:focus {
                box-shadow: 0 8px 25px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """)

    def style_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء والموردين"""
        try:
            # ألوان مطابقة تماماً للعملاء والموردين
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مطابق للعملاء والموردين
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 4px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.5px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.95);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               inset 0 2px 4px rgba(0, 0, 0, 0.4),
                               0 2px 8px {color_scheme['shadow']};
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم بسيط كبديل
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: #3b82f6;
                    color: white;
                    border: 2px solid #1d4ed8;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: #60a5fa;
                }}
                QPushButton:pressed {{
                    background-color: #1d4ed8;
                }}
            """)

    def update_items_table(self):
        """تحديث جدول العناصر مع تصميم محسن"""
        self.items_table.setRowCount(len(self.items))

        for row, item in enumerate(self.items):
            # الوصف
            desc_item = QTableWidgetItem(item.get('description', ''))
            desc_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 0, desc_item)

            # الكمية
            qty_item = QTableWidgetItem(str(item.get('quantity', 0)))
            qty_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 1, qty_item)

            # سعر الوحدة
            price_item = QTableWidgetItem(f"{item.get('unit_price', 0):.2f} جنيه")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.items_table.setItem(row, 2, price_item)

            # الإجمالي
            total_item = QTableWidgetItem(f"{item.get('total_price', 0):.2f} جنيه")
            total_item.setTextAlignment(Qt.AlignCenter)
            # تلوين الإجمالي بالأخضر
            total_item.setForeground(QColor(16, 185, 129))
            self.items_table.setItem(row, 3, total_item)

            # زر حذف مطابق للنمط الجديد
            delete_btn = QPushButton("🗑️")
            delete_btn.setToolTip("حذف هذا العنصر")
            delete_btn.clicked.connect(lambda checked, r=row: self.delete_item(r))
            delete_btn.setMinimumSize(30, 30)
            self.style_button(delete_btn, "danger")
            self.items_table.setCellWidget(row, 4, delete_btn)

        # ضبط عرض الأعمدة
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # الوصف
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # سعر الوحدة
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الإجمالي
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # حذف

    def update_total(self):
        """تحديث المجموع الإجمالي"""
        total = sum(item.get('total_price', 0) for item in self.items)
        self.total_label.setText(f"الإجمالي: {total:.2f} جنيه")

    def add_item(self):
        """إضافة عنصر جديد"""
        dialog = InvoiceItemDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            data = dialog.get_data()
            if data:
                self.items.append(data)
                self.update_items_table()
                self.update_total()

    def edit_item(self):
        """تعديل عنصر محدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0 and current_row < len(self.items):
            # إنشاء كائن مؤقت للتعديل
            temp_item = type('obj', (object,), self.items[current_row])
            dialog = InvoiceItemDialog(self, temp_item)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    self.items[current_row] = data
                    self.update_items_table()
                    self.update_total()

    def delete_item(self, row):
        """حذف عنصر"""
        if 0 <= row < len(self.items):
            del self.items[row]
            self.update_items_table()
            self.update_total()

    def delete_selected_item(self):
        """حذف العنصر المحدد من الجدول"""
        current_row = self.items_table.currentRow()
        if current_row >= 0 and current_row < len(self.items):
            # تأكيد الحذف
            from utils.helpers import show_confirmation_message
            if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟"):
                self.delete_item(current_row)
        else:
            # from utils.helpers import show_error_message  # لم تعد مستخدمة
            self.show_error_message("الرجاء اختيار عنصر من القائمة")

    def get_data(self):
        """الحصول على بيانات الفاتورة"""
        try:
            invoice_number = self.invoice_number_edit.text().strip()
            client_id = self.client_combo.currentData()
            date = qdate_to_datetime(self.date_edit.date())
            due_date = qdate_to_datetime(self.due_date_edit.date())
            status = self.status_combo.currentData()
            paid_amount = self.paid_amount_edit.value()
            notes = self.notes_edit.toPlainText().strip()

            # حساب المجموع الإجمالي
            total_amount = sum(item.get('total_price', 0) for item in self.items)

            # التحقق من صحة البيانات
            if not invoice_number:
                show_invoice_advanced_error(self, "خطأ", "يجب إدخال رقم الفاتورة")
                return None

            if not client_id:
                show_invoice_advanced_error(self, "خطأ", "يجب اختيار عميل")
                return None

            if not self.items:
                show_invoice_advanced_error(self, "خطأ", "يجب إضافة عنصر واحد على الأقل")
                return None

            return {
                'invoice_number': invoice_number,
                'client_id': client_id,
                'date': date,
                'due_date': due_date,
                'total_amount': total_amount,
                'paid_amount': paid_amount,
                'status': status,
                'notes': notes,
                'items': self.items
            }
        except Exception as e:
            show_invoice_advanced_error(self, "خطأ", f"حدث خطأ: {str(e)}")
            return None


class InvoicesWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة الفواتير مع التحديد المتعدد"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع فحص وجودها"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    method()
                    return True

            # إذا لم توجد الدالة، عرض رسالة
            self.show_info_message(f"ميزة {method_name} قيد التطوير")
            return False

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تنفيذ العملية: {str(e)}")
            return False



    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين
        title_label = QLabel("📋 إدارة الفواتير المتطورة - نظام شامل ومتقدم لإدارة الفواتير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث برقم الفاتورة، العميل أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_invoices)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QLineEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QLineEdit:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
        """)

        search_button = QPushButton("🔍")
        search_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                color: #ffffff;
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                padding: 8px;
                font-size: 22px;
                font-weight: 900;
                min-width: 50px;
                max-width: 50px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.95);
                transform: translateY(1px);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)
        search_button.clicked.connect(self.filter_invoices)
        search_button.setToolTip("بحث متقدم")
        search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        search_button.setContentsMargins(0, 0, 0, 0)

        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.7);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()


        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(search_button, 0, Qt.AlignVCenter)
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        # إنشاء جدول الفواتير المتطور والمحسن
        self.create_advanced_invoices_table()

        main_layout.addWidget(top_frame)
        main_layout.addWidget(self.invoices_table, 1)  # إعطاء الجدول أولوية في التمدد

        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار النظيفة والمرتبة

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة فاتورة")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_invoice)
        self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_invoice)
        self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_invoice)
        self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'modern_teal')
        self.refresh_button.clicked.connect(self.refresh_data)
        self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'cyan')  # لون مطابق للعملاء والموردين والعمال
        self.view_button.clicked.connect(self.view_invoice)
        self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)



        # زر التصدير
        self.export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.export_button, 'black', has_menu=True)  # لون مطابق للعملاء والموردين والعمال
        self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة التصدير المتقدمة مطابقة تماماً للعملاء والمصروفات
        export_menu = QMenu(self)
        export_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 12px;
                padding: 8px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 13px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
                           0 5px 15px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 200px;
            }
            QMenu::item {
                background: transparent;
                padding: 10px 5px 10px 5px;
                margin: 2px;
                border: none;
                border-radius: 8px;
                color: #ffffff;
                font-weight: 700;
                font-size: 13px;
                text-align: left;
                min-height: 20px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                padding-left: 110px !important;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 6px 12px;
                border: none;
                border-radius: 1px;
            }
        """)

        # قسم التصدير الأساسي - ربط مباشر للدوال الموجودة
        excel_action = QAction("📊 تصدير Excel متقدم", self)
        excel_action.triggered.connect(self.export_excel_advanced)
        export_menu.addAction(excel_action)

        csv_action = QAction("📄 تصدير CSV شامل", self)
        csv_action.triggered.connect(self.export_csv_advanced)
        export_menu.addAction(csv_action)

        pdf_action = QAction("📋 تصدير PDF تفصيلي", self)
        pdf_action.triggered.connect(self.export_pdf_advanced)
        export_menu.addAction(pdf_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التقارير المتقدمة
        monthly_report_action = QAction("📅 التقرير الشهري", self)
        monthly_report_action.triggered.connect(self.export_monthly_report)
        export_menu.addAction(monthly_report_action)

        client_report_action = QAction("👥 تقرير العملاء", self)
        client_report_action.triggered.connect(self.export_client_report)
        export_menu.addAction(client_report_action)

        payment_report_action = QAction("💰 تقرير المدفوعات", self)
        payment_report_action.triggered.connect(self.export_payment_report)
        export_menu.addAction(payment_report_action)

        # فاصل
        export_menu.addSeparator()

        # قسم التصدير المخصص
        custom_action = QAction("⚙️ تصدير مخصص", self)
        custom_action.triggered.connect(self.export_custom)
        export_menu.addAction(custom_action)

        backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        backup_action.triggered.connect(self.export_backup)
        export_menu.addAction(backup_action)

        restore_action = QAction("📥 استعادة نسخة احتياطية", self)
        restore_action.triggered.connect(self.restore_backup)
        export_menu.addAction(restore_action)

        # تخصيص موضع وعرض القائمة
        def show_export_menu():
            """عرض قائمة التصدير فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.export_button.mapToGlobal(self.export_button.rect().topLeft())

            # تحديد عرض القائمة مع تكبير ربع درجة
            button_width = self.export_button.width()
            export_menu.setFixedWidth(max(button_width, 190))

            # ترحيل القائمة نصف درجة لليسار
            button_pos.setX(button_pos.x() - 10)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = export_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            export_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة بدلاً من setMenu
        self.export_button.clicked.connect(show_export_menu)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 الإحصائيات")
        self.style_advanced_button(self.statistics_button, 'rose')
        self.statistics_button.clicked.connect(self.show_statistics)
        self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # زر إخفاء/إظهار الأعمدة
        self.columns_visibility_button = QPushButton("👁️ إدارة الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'indigo')
        self.columns_visibility_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        # إنشاء قائمة إدارة الأعمدة
        self.create_columns_visibility_menu()

        # إجمالي الفواتير مطور ليتشابه مع الأزرار مع حفظ المقاسات والخط الداخلي
        self.total_label = QLabel("إجمالي الفواتير: 0.00")
        self.total_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                padding: 8px 16px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #064e3b,
                    stop:0.1 #047857,
                    stop:0.9 #065f46,
                    stop:1 #10b981);
                border: 5px solid #10b981;
                border-radius: 20px;
                min-height: 34px;
                max-height: 38px;
                text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                           2px 2px 4px rgba(0, 0, 0, 0.7),
                           1px 1px 2px rgba(0, 0, 0, 0.5);
                box-shadow: 0 8px 20px rgba(16, 185, 129, 0.6),
                           inset 0 3px 0 rgba(255, 255, 255, 0.4),
                           inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                           0 0 25px rgba(16, 185, 129, 0.6),
                           0 0 40px rgba(255, 255, 255, 0.1);
                letter-spacing: 0.5px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        """)
        self.total_label.setAlignment(Qt.AlignCenter)

        # إضافة الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.columns_visibility_button)
        actions_layout.addWidget(self.total_label)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        main_layout.addWidget(bottom_frame)

        self.setLayout(main_layout)

        # ربط الأحداث وتهيئة حالة الأزرار
        self.connect_events()

        # تهيئة حالة الأزرار (جميع الأزرار مفعلة ومنيرة في البداية)
        QTimer.singleShot(100, self.initialize_button_states)

        # تأجيل تحميل البيانات لتحسين الأداء
        QTimer.singleShot(550, self.refresh_data)

    def create_advanced_invoices_table(self):
        """إنشاء جدول الفواتير المتطور والنظيف"""
        # إنشاء الجدول
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(8)

        # عناوين الأعمدة مع الأيقونات
        headers = [
            "🔢 ID",
            "📋 رقم الفاتورة",
            "🧑‍💼 العميل",
            "📆 تاريخ الإنشاء",
            "⏳ موعد الاستحقاق",
            "💎 إجمالي المبلغ",
            "💵 المبلغ المسدد",
            "🎯 حالة الدفع"
        ]
        self.invoices_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مع التحديد المتعدد
        self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoices_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoices_table.setAlternatingRowColors(False)
        self.invoices_table.setSortingEnabled(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.invoices_table)

        # إعدادات الصفوف والأعمدة
        self.invoices_table.verticalHeader().setDefaultSectionSize(50)
        self.invoices_table.verticalHeader().setVisible(False)

        header = self.invoices_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.invoices_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.invoices_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم والتفاعل
        self.apply_table_style()
        self.add_watermark_to_table()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول"""
        self.invoices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(44, 62, 80, 0.2);
                background: #e2e8f0;
                border: 3px solid #000000;
                border-radius: 20px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: 500;
                selection-background-color: rgba(102, 126, 234, 0.15);
                alternate-background-color: rgba(203, 213, 225, 0.3);
                outline: none;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def add_watermark_to_table(self):
        """إضافة علامة مائية مبسطة للجدول"""
        try:
            # علامة مائية مبسطة بدون تعقيد في الرسم
            pass  # تم تعطيل العلامة المائية المعقدة مؤقتاً
        except Exception as e:
            print(f"تحذير: فشل في إضافة العلامة المائية: {e}")

    def setup_table_interactions(self):
        """إعداد التفاعلات مع الجدول"""
        self.invoices_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.invoices_table.cellDoubleClicked.connect(self.edit_invoice)

        # إلغاء التحديد عند النقر على منطقة فارغة
        def mousePressEvent(event):
            item = self.invoices_table.itemAt(event.pos())
            if item is None:
                self.invoices_table.clearSelection()
            QTableWidget.mousePressEvent(self.invoices_table, event)

        self.invoices_table.mousePressEvent = mousePressEvent

        # إضافة معالج التمرير المخصص (يحاكي سلوك الأسهم)
        def wheelEvent(event):
            try:
                # التمرير العمودي بالماوس
                delta = event.angleDelta().y()

                # تجاهل الحركات الصغيرة جداً
                if abs(delta) < 120:
                    event.accept()
                    return

                # الحصول على شريط التمرير
                scrollbar = self.invoices_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                # محاكاة سلوك الأسهم - خطوة واحدة في كل مرة
                if delta > 0:
                    # التمرير لأعلى - مثل الضغط على السهم العلوي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    # التمرير لأسفل - مثل الضغط على السهم السفلي
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()

            except Exception:
                # في حالة الخطأ، استخدم التمرير الافتراضي
                QTableWidget.wheelEvent(self.invoices_table, event)

        self.invoices_table.wheelEvent = wheelEvent

    def on_selection_changed(self):
        """معالج تغيير التحديد في الجدول"""
        try:
            print("🚨 تم استدعاء معالج تحديد الفواتير!")

            # تسجيل أن المستخدم تفاعل مع الجدول
            self.user_interacted_with_table = True
            print("👆 المستخدم تفاعل مع الجدول - سيتم تطبيق خاصية الإغلاق")

            self.update_button_states()

        except Exception as e:
            print(f"❌ خطأ في معالجة تحديد الفواتير: {e}")
            import traceback
            print(traceback.format_exc())

    def update_button_states(self):
        """تحديث حالة الأزرار حسب التحديد مطابق للمشاريع"""
        try:
            # إذا لم يتفاعل المستخدم مع الجدول بعد، لا نغير حالة الأزرار
            if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                print("🔥 المستخدم لم يتفاعل مع الجدول بعد - الأزرار تبقى مفعلة")
                return

            selected_items = self.invoices_table.selectedItems()
            has_selection = len(selected_items) > 0

            print(f"🔧 تحديث حالة الأزرار: {'يوجد تحديد' if has_selection else 'لا يوجد تحديد'}")

            # الأزرار التي تحتاج تحديد عنصر
            self.set_button_visibility(self.edit_button, has_selection)
            self.set_button_visibility(self.delete_button, has_selection)
            self.set_button_visibility(self.view_button, has_selection)

            # الأزرار المتاحة دائماً
            self.set_button_visibility(self.add_button, True)  # زر الإضافة متاح دائماً

        except Exception as e:
            print(f"❌ خطأ في تحديث حالة الأزرار: {e}")

    def set_button_visibility(self, button, visible):
        """تعيين رؤية الزر مع الحفاظ على الألوان الأصلية - مطابق للمشاريع"""
        try:
            if button:
                # التحقق من أن المستخدم تفاعل مع الجدول قبل تطبيق التأثيرات
                if not hasattr(self, 'user_interacted_with_table') or not self.user_interacted_with_table:
                    # إذا لم يتفاعل المستخدم بعد، نبقي الزر مفعلاً ومنيراً
                    button.setEnabled(True)
                    button.setGraphicsEffect(None)
                    print(f"🔥 زر {button.text()}: مفعل (لم يتفاعل المستخدم مع الجدول بعد)")
                    return

                button.setEnabled(visible)

                # تطبيق تأثير الشفافية مع الحفاظ على الألوان - طريقة محسنة
                current_style = button.styleSheet()
                import re
                # إزالة أي opacity موجودة سابقاً
                clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)

                if visible:
                    # إظهار الزر بشفافية كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                else:
                    # تقليل شفافية الزر (لا نخفيه تماماً)
                    new_style = clean_style + "\nQPushButton { opacity: 0.3; }"

                button.setStyleSheet(new_style)

                print(f"🔧 زر {button.text()}: {'مفعل' if visible else 'معطل'}")
        except Exception as e:
            print(f"❌ خطأ في تعيين رؤية الزر: {e}")
            # في حالة فشل التأثير، استخدم الطريقة البسيطة
            if button:
                button.setEnabled(True)  # نبقيه مفعلاً في حالة الخطأ

    def connect_events(self):
        """ربط جميع الأحداث بعد إنشاء جميع العناصر"""
        try:
            if hasattr(self, 'invoices_table'):
                # ربط حدث تغيير التحديد لتفعيل/تعطيل الأزرار
                self.invoices_table.itemSelectionChanged.connect(self.on_selection_changed)
                self.invoices_table.cellDoubleClicked.connect(self.edit_invoice)

                # إلغاء التحديد عند النقر على منطقة فارغة
                def mousePressEvent(event):
                    item = self.invoices_table.itemAt(event.pos())
                    if item is None:
                        self.invoices_table.clearSelection()
                    QTableWidget.mousePressEvent(self.invoices_table, event)

                self.invoices_table.mousePressEvent = mousePressEvent

                # إضافة معالج التمرير المخصص
                def wheelEvent(event):
                    try:
                        from PyQt5.QtWidgets import QAbstractSlider
                        delta = event.angleDelta().y()
                        if abs(delta) < 120:
                            event.accept()
                            return
                        scrollbar = self.invoices_table.verticalScrollBar()
                        if not scrollbar:
                            event.accept()
                            return
                        if delta > 0:
                            scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                        else:
                            scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)
                        event.accept()
                    except Exception:
                        QTableWidget.wheelEvent(self.invoices_table, event)

                self.invoices_table.wheelEvent = wheelEvent

            print("✅ تم ربط أحداث الفواتير بنجاح")
        except Exception as e:
            print(f"❌ خطأ في ربط أحداث الفواتير: {str(e)}")

    def initialize_button_states(self):
        """تهيئة حالة الأزرار عند البداية - جميع الأزرار منيرة ومفعلة"""
        try:
            print("🔧 بدء تهيئة حالة أزرار الفواتير...")

            # تفعيل جميع الأزرار وجعلها منيرة
            buttons = [
                (self.add_button, "➕ إضافة فاتورة"),
                (self.edit_button, "✏️ تعديل"),
                (self.delete_button, "🗑️ حذف"),
                (self.refresh_button, "🔄 تحديث"),
                (self.view_button, "👁️ عرض التفاصيل"),
                (self.export_button, "📤 تصدير"),
                (self.statistics_button, "📊 الإحصائيات"),
                (self.columns_visibility_button, "👁️ إدارة الأعمدة")
            ]

            for button, name in buttons:
                if button:
                    button.setEnabled(True)
                    # إزالة أي تأثيرات شفافية سابقة وتطبيق الشفافية الكاملة
                    current_style = button.styleSheet()
                    # إزالة أي opacity موجودة
                    import re
                    clean_style = re.sub(r'opacity:\s*[\d.]+;?', '', current_style)
                    # إضافة opacity كاملة
                    new_style = clean_style + "\nQPushButton { opacity: 1.0; }"
                    button.setStyleSheet(new_style)
                    button.show()
                    print(f"🟢 تم تفعيل الزر: {name}")

            print("✅ تم تهيئة حالة أزرار الفواتير بنجاح")

            # لا نعطل الأزرار تلقائياً - تبقى مفعلة حتى يتفاعل المستخدم مع الجدول
            print("🔥 الأزرار ستبقى مفعلة حتى التفاعل مع الجدول")

        except Exception as e:
            print(f"❌ خطأ في تهيئة حالة أزرار الفواتير: {str(e)}")

    def get_selected_invoice_id(self):
        """استخراج معرف الفاتورة المحددة من الجدول"""
        try:
            selected_row = self.invoices_table.currentRow()
            if selected_row < 0:
                return None, "الرجاء اختيار فاتورة من القائمة"

            if not self.invoices_table.item(selected_row, 0):
                return None, "الرجاء اختيار فاتورة صالحة من القائمة"

            # استخراج الرقم من النص (إزالة # والأيقونات)
            id_text = self.invoices_table.item(selected_row, 0).text()
            numbers = re.findall(r'\d+', id_text)
            if not numbers:
                return None, "لا يمكن استخراج رقم الفاتورة"

            return int(numbers[0]), None
        except Exception as e:
            return None, f"خطأ في استخراج معرف الفاتورة: {str(e)}"



    def refresh_data(self):
        """تحديث بيانات الفواتير في الجدول مع حماية من الضغط المتكرر"""
        try:
            # منع الضغط المتكرر على الزر
            if hasattr(self, '_is_refreshing') and self._is_refreshing:
                return

            # تعيين حالة التحديث
            self._is_refreshing = True

            # تعطيل زر التحديث مؤقتاً
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(False)
                self.refresh_button.setText("🔄 جاري التحديث...")

            # الحصول على جميع الفواتير من قاعدة البيانات
            invoices = self.session.query(Invoice).all()
            self.populate_table(invoices)
            self.update_summary(invoices)

        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تحديث البيانات: {str(e)}")
        finally:
            # إعادة تفعيل زر التحديث وإعادة تعيين النص
            if hasattr(self, 'refresh_button'):
                self.refresh_button.setEnabled(True)
                self.refresh_button.setText("🔄 تحديث")

            # إعادة تعيين حالة التحديث
            self._is_refreshing = False

    def get_invoice_status(self, status):
        """تحديد حالة الفاتورة مطابق للعملاء"""
        status_map = {
            'pending': '🟡 قيد الانتظار',
            'paid': '🟢 مدفوعة بالكامل',
            'partially_paid': '🟢 مدفوعة جزئياً',
            'cancelled': '🔴 ملغاة'
        }
        return status_map.get(status, '🟡 غير محدد')

    def populate_table(self, invoices):
        """ملء جدول الفواتير بالبيانات"""
        try:
            # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء
            self.invoices_table.setUpdatesEnabled(False)

            # مسح الجدول
            self.invoices_table.setRowCount(0)

            # إضافة الصفوف مع تنسيق محسن
            for row, invoice in enumerate(invoices):
                try:
                    self.invoices_table.insertRow(row)

                    # 1. الرقم التسلسلي مع أيقونة ثابتة مطابق للعملاء
                    # الفواتير دائماً تستخدم أيقونة 🔢 لأنها لا تحتوي على رصيد
                    id_item = QTableWidgetItem(f"🔢 {invoice.id}")
                    id_item.setTextAlignment(Qt.AlignCenter)
                    id_item.setForeground(QColor("#000000"))  # لون أسود للرقم مطابق للعملاء
                    self.invoices_table.setItem(row, 0, id_item)

                    # دالة مساعدة لإنشاء العناصر مطابق للعملاء
                    def create_item(icon, text, default="No Data"):
                        display_text = text if text and text.strip() else default
                        item = QTableWidgetItem(f"{icon} {display_text}")
                        item.setTextAlignment(Qt.AlignCenter)
                        if display_text == default:
                            item.setForeground(QColor("#ef4444"))
                        return item

                    invoice_num = invoice.invoice_number or f"INV-{str(invoice.id).zfill(6)}"
                    client_name = invoice.client.name if invoice.client else None

                    self.invoices_table.setItem(row, 1, create_item("📋", invoice_num))
                    self.invoices_table.setItem(row, 2, create_item("🧑‍💼", client_name))

                    # التواريخ مطابق للعملاء
                    date_text = invoice.date.strftime("%Y-%m-%d") if invoice.date else None
                    due_date_text = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else None

                    self.invoices_table.setItem(row, 3, create_item("📆", date_text))
                    self.invoices_table.setItem(row, 4, create_item("⏳", due_date_text))

                    # المبالغ مطابق للعملاء
                    from utils import format_currency
                    total_text = format_currency(invoice.total_amount) if invoice.total_amount else None
                    paid_text = format_currency(invoice.paid_amount) if invoice.paid_amount else None

                    self.invoices_table.setItem(row, 5, create_item("💰", total_text))
                    self.invoices_table.setItem(row, 6, create_item("💵", paid_text))

                    # 8. حالة الدفع مطابق للعملاء
                    status_item = QTableWidgetItem(self.get_invoice_status(invoice.status))
                    status_item.setTextAlignment(Qt.AlignCenter)
                    self.invoices_table.setItem(row, 7, status_item)
                except Exception as row_error:
                    # تجاهل الصف الذي به خطأ والاستمرار في العملية
                    print(f"خطأ في الصف {row}: {str(row_error)}")
                    continue

            # إعادة تمكين تحديث الجدول
            self.invoices_table.setUpdatesEnabled(True)
        except Exception as e:
            # إعادة تمكين تحديث الجدول في حالة حدوث خطأ
            self.invoices_table.setUpdatesEnabled(True)
            self.show_error_message(f"حدث خطأ أثناء تحديث جدول الفواتير: {str(e)}")

    def update_summary(self, invoices):
        """تحديث ملخص الفواتير"""
        try:
            # حساب المبالغ
            total = sum(invoice.total_amount or 0 for invoice in invoices)
            paid = sum(invoice.paid_amount or 0 for invoice in invoices)
            balance = total - paid

            # تحديث النصوص
            self.total_label.setText(f"إجمالي الفواتير: {format_currency(total)} | المدفوعات: {format_currency(paid)} | المستحقات: {format_currency(balance)}")
        except Exception as e:
            # في حالة حدوث خطأ، عرض قيم افتراضية
            self.total_label.setText("إجمالي الفواتير: 0.00 | المدفوعات: 0.00 | المستحقات: 0.00")
            print(f"خطأ في تحديث ملخص الفواتير: {str(e)}")

    def filter_invoices(self):
        """تصفية الفواتير بناءً على نص البحث والحالة"""
        try:
            search_text = self.search_edit.text().strip().lower()
            status = getattr(self, 'current_filter_value', None)

            # بناء الاستعلام
            query = self.session.query(Invoice).join(Client, Invoice.client_id == Client.id, isouter=True)

            # تطبيق تصفية النص
            if search_text:
                query = query.filter(
                    Invoice.invoice_number.like(f"%{search_text}%") |
                    Client.name.like(f"%{search_text}%") |
                    Invoice.notes.like(f"%{search_text}%")
                )

            # تطبيق تصفية الحالة
            if status:
                query = query.filter(Invoice.status == status)

            # تنفيذ الاستعلام
            invoices = query.all()

            # تحديث الجدول والملخص
            self.populate_table(invoices)
            self.update_summary(invoices)
        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصفية الفواتير: {str(e)}")

    def add_invoice(self):
        """إنشاء فاتورة جديدة"""
        try:
            dialog = InvoiceDialog(self, session=self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # إنشاء فاتورة جديدة في قاعدة البيانات
                    invoice = Invoice(**data)
                    self.session.add(invoice)
                    self.session.flush()  # للحصول على معرف الفاتورة

                    # إضافة عناصر الفاتورة
                    for item_data in items_data:
                        if all(key in item_data for key in ['description', 'quantity', 'unit_price', 'total_price']):
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()

                    self.show_success_message("تم إنشاء الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            self.show_error_message(f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")

    def edit_invoice(self):
        """تعديل بيانات فاتورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                self.show_error_message(error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                self.show_error_message("لم يتم العثور على الفاتورة")
                return

            dialog = InvoiceDialog(self, invoice, self.session)
            if dialog.exec_() == QDialog.Accepted:
                data = dialog.get_data()
                if data:
                    # استخراج عناصر الفاتورة
                    items_data = data.pop('items')

                    # تحديث بيانات الفاتورة
                    for key, value in data.items():
                        setattr(invoice, key, value)

                    # حذف جميع عناصر الفاتورة الحالية
                    for item in invoice.items:
                        self.session.delete(item)

                    # إضافة عناصر الفاتورة الجديدة
                    for item_data in items_data:
                        if all(key in item_data for key in ['description', 'quantity', 'unit_price', 'total_price']):
                            item_data['invoice_id'] = invoice.id
                            item = InvoiceItem(**item_data)
                            self.session.add(item)

                    self.session.commit()
                    self.show_success_message("تم تحديث بيانات الفاتورة بنجاح")
                    self.refresh_data()
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            self.show_error_message(f"حدث خطأ أثناء تعديل الفاتورة: {str(e)}")

    def delete_selected_items(self):
        """حذف الفواتير المحددة"""
        try:
            self.update_selected_items()
            if not self.selected_items:
                return

            count = len(self.selected_items)
            if count == 1:
                self.delete_invoice()
            else:
                if show_confirmation_message("تأكيد الحذف", f"هل تريد حذف {count} فاتورة؟"):
                    for item_id in self.selected_items:
                        invoice = self.session.query(Invoice).get(item_id)
                        if invoice:
                            self.session.delete(invoice)
                    self.session.commit()
                    self.load_invoices()
        except Exception as e:
            print(f"خطأ في حذف الفواتير: {e}")

    def show_context_menu(self, position):
        """عرض القائمة السياقية للفواتير"""
        try:
            menu = QMenu(self)

            single_actions = [
                ("✏️ تعديل", self.edit_invoice),
                ("👁️ عرض التفاصيل", self.view_invoice_details),
                ("🗑️ حذف", self.delete_invoice)
            ]

            multi_actions = [
                ("🗑️ حذف {count} فاتورة", self.delete_selected_items)
            ]

            self.create_context_menu_actions(menu, single_actions, multi_actions)
            menu.exec_(self.invoices_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في القائمة السياقية: {e}")

    def delete_invoice(self):
        """حذف فاتورة مع نافذة تأكيد متطورة"""
        try:
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                self.show_warning_message(error)
                return

            invoice = self.session.query(Invoice).get(invoice_id)
            if not invoice:
                self.show_error_message("لم يتم العثور على الفاتورة")
                return

            # التحقق من وجود إيرادات مرتبطة بالفاتورة
            if invoice.revenues:
                self.show_error_message(
                    f"لا يمكن حذف الفاتورة لأنها مرتبطة بـ {len(invoice.revenues)} إيراد. قم بحذف الإيرادات أولاً."
                )
                return

            # إنشاء نافذة حذف متطورة مشابهة للعملاء
            dialog = DeleteInvoiceDialog(self, invoice)
            if dialog.exec_() == QDialog.Accepted:
                try:
                    # حذف جميع عناصر الفاتورة
                    for item in invoice.items:
                        self.session.delete(item)

                    # حذف الفاتورة
                    self.session.delete(invoice)
                    self.session.commit()

                    # إظهار رسالة نجاح متطورة
                    self.show_success_message(f"تم حذف الفاتورة '{invoice.invoice_number}' بنجاح")

                    # تحديث الجدول
                    self.refresh_data()

                except Exception as e:
                    self.session.rollback()
                    self.show_error_message(f"فشل في حذف الفاتورة: {str(e)}")
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            self.session.rollback()
            self.show_error_message(f"خطأ في حذف الفاتورة: {str(e)}")

    def show_warning_message(self, message):
        """إظهار رسالة تحذير متطورة مطابقة لنافذة الحذف"""
        show_invoice_advanced_warning(self, "تحذير", message)

    def show_success_message(self, message):
        """إظهار رسالة نجاح متطورة مطابقة لنافذة الحذف"""
        show_invoice_advanced_info(self, "تم", message, "✅")

    def show_error_message(self, message):
        """إظهار رسالة خطأ متطورة مطابقة لنافذة الحذف"""
        show_invoice_advanced_error(self, "خطأ", message)

    def show_info_message(self, message):
        """إظهار رسالة معلومات متطورة مطابقة لنافذة الحذف"""
        show_invoice_advanced_info(self, "معلومات", message)

    def view_invoice(self):
        """عرض تفاصيل الفاتورة مع معالجة محسنة للأخطاء"""
        try:
            print("🔍 بدء عرض تفاصيل الفاتورة...")

            # التحقق من وجود جلسة قاعدة البيانات
            if not hasattr(self, 'session') or not self.session:
                self.show_error_message("لا توجد جلسة قاعدة بيانات نشطة")
                return

            # الحصول على معرف الفاتورة المحددة
            invoice_id, error = self.get_selected_invoice_id()
            if error:
                print(f"❌ خطأ في الحصول على معرف الفاتورة: {error}")
                self.show_error_message(error)
                return

            print(f"✅ تم الحصول على معرف الفاتورة: {invoice_id}")

            # البحث عن الفاتورة في قاعدة البيانات
            try:
                invoice = self.session.query(Invoice).get(invoice_id)
                if not invoice:
                    self.show_error_message("لم يتم العثور على الفاتورة")
                    return
                print(f"✅ تم العثور على الفاتورة: {invoice.invoice_number}")
            except Exception as db_error:
                print(f"❌ خطأ في قاعدة البيانات: {str(db_error)}")
                self.show_error_message(f"خطأ في قاعدة البيانات: {str(db_error)}")
                return

            # إنشاء نافذة لعرض تفاصيل الفاتورة
            try:
                dialog = QDialog(self)
                dialog.setWindowTitle(f"تفاصيل الفاتورة - {invoice.invoice_number}")
                dialog.setMinimumSize(800, 600)
                dialog.setModal(True)

                layout = QVBoxLayout()

                # إنشاء مستعرض نصي لعرض تفاصيل الفاتورة
                text_browser = QTextBrowser()
                text_browser.setOpenExternalLinks(False)

                # إنشاء محتوى HTML مع معالجة الأخطاء
                try:
                    html_content = self.generate_invoice_html(invoice)
                    text_browser.setHtml(html_content)
                    print("✅ تم إنشاء محتوى HTML بنجاح")
                except Exception as html_error:
                    print(f"❌ خطأ في إنشاء HTML: {str(html_error)}")
                    # عرض محتوى بديل بسيط
                    simple_content = f"""
                    <h2>تفاصيل الفاتورة</h2>
                    <p><strong>رقم الفاتورة:</strong> {invoice.invoice_number}</p>
                    <p><strong>التاريخ:</strong> {invoice.date.strftime('%Y-%m-%d') if invoice.date else 'غير محدد'}</p>
                    <p><strong>العميل:</strong> {invoice.client.name if invoice.client else 'غير محدد'}</p>
                    <p><strong>المبلغ الإجمالي:</strong> {invoice.total_amount} جنيه</p>
                    <p><strong>الحالة:</strong> {invoice.status or 'غير محدد'}</p>
                    <p style="color: red;">خطأ في تحميل التفاصيل الكاملة: {str(html_error)}</p>
                    """
                    text_browser.setHtml(simple_content)

                layout.addWidget(text_browser)

                # أزرار الإجراءات
                button_layout = QHBoxLayout()
                button_layout.setSpacing(10)

                # زر إغلاق (الأهم)
                close_button = QPushButton("❌ إغلاق")
                close_button.setMinimumWidth(120)
                close_button.clicked.connect(dialog.accept)
                close_button.setStyleSheet("""
                    QPushButton {
                        background-color: #dc2626;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px 16px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #b91c1c;
                    }
                """)

                # زر طباعة
                print_button = QPushButton("🖨️ طباعة")
                print_button.setMinimumWidth(120)
                print_button.clicked.connect(lambda: self.safe_print_invoice(invoice))
                print_button.setStyleSheet("""
                    QPushButton {
                        background-color: #3b82f6;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        padding: 8px 16px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #2563eb;
                    }
                """)

                button_layout.addStretch()
                button_layout.addWidget(print_button)
                button_layout.addWidget(close_button)

                layout.addLayout(button_layout)
                dialog.setLayout(layout)

                print("✅ تم إنشاء النافذة بنجاح")
                dialog.exec_()
                print("✅ تم إغلاق النافذة بنجاح")

            except Exception as dialog_error:
                print(f"❌ خطأ في إنشاء النافذة: {str(dialog_error)}")
                self.show_error_message(f"خطأ في إنشاء نافذة العرض: {str(dialog_error)}")

        except Exception as e:
            print(f"❌ خطأ عام في عرض الفاتورة: {str(e)}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")
            self.show_error_message(f"حدث خطأ أثناء عرض الفاتورة: {str(e)}")

    def safe_print_invoice(self, invoice):
        """طباعة آمنة للفاتورة"""
        try:
            print("🖨️ بدء الطباعة...")
            # يمكن إضافة كود الطباعة هنا لاحقاً
            self.show_info_message("ميزة الطباعة ستكون متاحة قريباً")
        except Exception as e:
            print(f"❌ خطأ في الطباعة: {str(e)}")
            self.show_error_message(f"خطأ في الطباعة: {str(e)}")

    def generate_invoice_html(self, invoice):
        """إنشاء محتوى HTML للفاتورة مع معالجة محسنة للأخطاء"""
        try:
            print("🔄 بدء إنشاء محتوى HTML للفاتورة...")

            # التحقق من صحة الفاتورة
            if not invoice:
                raise ValueError("الفاتورة غير صالحة")

            print(f"✅ الفاتورة صالحة: {invoice.invoice_number}")
            # ترجمة حالة الفاتورة
            status_map = {
                'pending': 'قيد الانتظار',
                'paid': 'مدفوعة',
                'partially_paid': 'مدفوعة جزئيًا',
                'cancelled': 'ملغاة'
            }
            status_text = status_map.get(invoice.status, invoice.status or "")

            # تنسيق التواريخ
            date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
            due_date_str = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else ""

            # إنشاء جدول عناصر الفاتورة
            items_html = """
            <table border="1" cellpadding="5" cellspacing="0" width="100%">
                <tr style="background-color: #f2f2f2;">
                    <th>السعر الإجمالي</th>
                    <th>سعر الوحدة</th>
                    <th>الكمية</th>
                    <th>الوصف</th>
                </tr>
            """

            # إضافة عناصر الفاتورة
            if invoice.items:
                for item in invoice.items:
                    items_html += f"""
                    <tr>
                        <td>{format_currency(item.total_price)}</td>
                        <td>{format_currency(item.unit_price)}</td>
                        <td>{format_quantity(item.quantity)}</td>
                        <td>{item.description}</td>
                    </tr>
                    """
            else:
                items_html += """
                <tr>
                    <td colspan="4" style="text-align: center;">لا توجد عناصر</td>
                </tr>
                """

            items_html += "</table>"

            # حساب الرصيد المتبقي
            total_amount = invoice.total_amount or 0
            paid_amount = invoice.paid_amount or 0
            balance = total_amount - paid_amount

            # بيانات العميل
            client_name = invoice.client.name if invoice.client else ""
            client_address = invoice.client.address if invoice.client else ""
            client_phone = invoice.client.phone if invoice.client else ""

            # إنشاء محتوى HTML كامل للفاتورة
            html = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; }}
                    .invoice-header {{ text-align: center; margin-bottom: 20px; }}
                    .invoice-details {{ margin-bottom: 20px; }}
                    .invoice-details table {{ width: 100%; }}
                    .invoice-items {{ margin-bottom: 20px; }}
                    .invoice-summary {{ text-align: left; margin-top: 20px; }}
                    .invoice-notes {{ margin-top: 30px; border-top: 1px solid #ccc; padding-top: 10px; }}
                </style>
            </head>
            <body>
                <div class="invoice-header">
                    <h1>فاتورة</h1>
                    <h2>{invoice.invoice_number}</h2>
                </div>

                <div class="invoice-details">
                    <table width="100%" style="border-collapse: collapse;">
                        <tr>
                            <td width="33%" style="text-align: right;"><strong>العميل:</strong> {client_name}</td>
                            <td width="34%" style="text-align: center;"><strong>العنوان:</strong> {client_address}</td>
                            <td width="33%" style="text-align: left;"><strong>التاريخ:</strong> {date_str}</td>
                        </tr>
                        <tr>
                            <td style="text-align: right;"><strong>الهاتف:</strong> {client_phone}</td>
                            <td style="text-align: center;"><strong>الحالة:</strong> {status_text}</td>
                            <td style="text-align: left;"><strong>تاريخ الدفع القادم:</strong> {due_date_str}</td>
                        </tr>
                    </table>
                </div>

                <div class="invoice-items">
                    <h3>عناصر الفاتورة</h3>
                    {items_html}
                </div>

                <div class="invoice-summary">
                    <p><strong>المبلغ الإجمالي:</strong> {format_currency(total_amount)}</p>
                    <p><strong>المبلغ المدفوع:</strong> {format_currency(paid_amount)}</p>
                    <p><strong>الرصيد المتبقي:</strong> {format_currency(balance)}</p>
                </div>

                <div class="invoice-notes">
                    <h3>ملاحظات</h3>
                    <p>{invoice.notes or ""}</p>
                </div>
            </body>
            </html>
            """

            print("✅ تم إنشاء محتوى HTML بنجاح")
            return html
        except Exception as e:
            print(f"❌ خطأ في إنشاء HTML: {str(e)}")
            import traceback
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")

            # إرجاع محتوى HTML بديل مع معلومات أساسية
            try:
                basic_info = f"""
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تفاصيل الفاتورة</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        .error {{ color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }}
                        .info {{ background: #f0f8ff; padding: 10px; border-radius: 5px; margin: 10px 0; }}
                    </style>
                </head>
                <body>
                    <h1>تفاصيل الفاتورة</h1>
                    <div class="info">
                        <p><strong>رقم الفاتورة:</strong> {getattr(invoice, 'invoice_number', 'غير محدد')}</p>
                        <p><strong>التاريخ:</strong> {invoice.date.strftime('%Y-%m-%d') if hasattr(invoice, 'date') and invoice.date else 'غير محدد'}</p>
                        <p><strong>العميل:</strong> {invoice.client.name if hasattr(invoice, 'client') and invoice.client else 'غير محدد'}</p>
                        <p><strong>المبلغ الإجمالي:</strong> {getattr(invoice, 'total_amount', 0)} جنيه</p>
                        <p><strong>الحالة:</strong> {getattr(invoice, 'status', 'غير محدد')}</p>
                    </div>
                    <div class="error">
                        <h3>تحذير</h3>
                        <p>حدث خطأ أثناء تحميل التفاصيل الكاملة للفاتورة.</p>
                        <p>رسالة الخطأ: {str(e)}</p>
                        <p>يتم عرض المعلومات الأساسية فقط.</p>
                    </div>
                </body>
                </html>
                """
                return basic_info
            except Exception as fallback_error:
                print(f"❌ خطأ في إنشاء المحتوى البديل: {str(fallback_error)}")
                return f"""
                <html dir="rtl">
                <body>
                    <h1>خطأ في عرض الفاتورة</h1>
                    <p>حدث خطأ أثناء محاولة عرض تفاصيل الفاتورة.</p>
                    <p>رسالة الخطأ الأصلي: {str(e)}</p>
                    <p>رسالة خطأ المحتوى البديل: {str(fallback_error)}</p>
                </body>
                </html>
                """



    def export_invoice_to_pdf(self, invoice, parent_dialog=None):
        """تصدير الفاتورة إلى ملف PDF"""
        try:
            # اختيار مكان حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                parent_dialog or self,
                "حفظ الفاتورة كـ PDF",
                f"فاتورة_{invoice.invoice_number}.pdf",
                "PDF Files (*.pdf)"
            )

            if not file_path:
                return  # المستخدم ألغى العملية

            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(file_path)
            printer.setPageSize(QPrinter.A4)
            printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter)

            # إنشاء مستند نصي
            document = QTextDocument()
            html_content = self.generate_invoice_html(invoice)
            document.setHtml(html_content)

            # طباعة المستند إلى PDF
            document.print_(printer)

            self.show_success_message(f"تم تصدير الفاتورة بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصدير الفاتورة إلى PDF: {str(e)}")

    def print_invoice_from_dialog(self, invoice):
        """طباعة الفاتورة من نافذة التفاصيل"""
        try:
            # إنشاء مستند للطباعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)

            # إظهار مربع حوار الطباعة
            dialog = QPrintDialog(printer, self)
            if dialog.exec_() == QDialog.Accepted:
                # إنشاء مستند نصي
                document = QTextDocument()
                html_content = self.generate_invoice_html(invoice)
                document.setHtml(html_content)
                # طباعة المستند
                document.print_(printer)
        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء طباعة الفاتورة: {str(e)}")

    def export_to_excel(self):
        """تصدير بيانات الفواتير إلى Excel"""
        try:
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ ملف Excel", "الفواتير.csv", "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    writer.writerow(['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة'])

                    # كتابة البيانات
                    for invoice in invoices:
                        date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else ""
                        client_name = invoice.client.name if invoice.client else ""
                        remaining = invoice.total_amount - invoice.paid_amount

                        status_names = {
                            'pending': 'معلقة',
                            'partially_paid': 'مدفوعة جزئياً',
                            'paid': 'مدفوعة بالكامل'
                        }
                        status_text = status_names.get(invoice.status, invoice.status or 'معلقة')

                        writer.writerow([
                            invoice.invoice_number or f"INV-{invoice.id}",
                            client_name,
                            date_str,
                            invoice.total_amount,
                            invoice.paid_amount,
                            remaining,
                            status_text
                        ])

                self.show_success_message(f"تم تصدير الفواتير بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير: {str(e)}")

    def export_to_csv(self):
        """تصدير بيانات الفواتير إلى CSV"""
        try:
            import csv

            # عرض مربع حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الفواتير.csv", "ملفات CSV (*.csv)")
            if not file_path:
                return

            # جمع البيانات من الجدول
            data = []
            headers = []

            # الحصول على عناوين الأعمدة
            for col in range(self.invoices_table.columnCount()):
                headers.append(self.invoices_table.horizontalHeaderItem(col).text())

            # جمع البيانات من الجدول
            for row in range(self.invoices_table.rowCount()):
                row_data = []
                for col in range(self.invoices_table.columnCount()):
                    item = self.invoices_table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)

            # كتابة البيانات إلى ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)

            self.show_success_message(f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
        except Exception as e:
            self.show_error_message(f"حدث خطأ أثناء تصدير البيانات: {str(e)}")







    def show_statistics(self):
        """عرض نافذة إحصائيات الفواتير"""
        try:
            dialog = InvoicesStatisticsDialog(self.session, self)
            dialog.exec_()
        except Exception as e:
            print(f"خطأ في عرض إحصائيات الفواتير: {e}")
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}")

    def create_columns_visibility_menu(self):
        """إنشاء قائمة إدارة إخفاء/إظهار الأعمدة"""
        # إنشاء قائمة إدارة الأعمدة
        self.columns_menu = QMenu(self)

        # تحديد عرض القائمة مع نفس ألوان وخلفية نافذة الإحصائيات
        self.columns_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 8px;
                padding: 4px;
                color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
                font-weight: 900;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3),
                           0 2px 8px rgba(59, 130, 246, 0.2),
                           inset 0 1px 0 rgba(255, 255, 255, 0.1);
                min-width: 160px;
            }
            QMenu::item {
                background: transparent;
                padding: 6px 25px 6px 15px;
                margin: 1px;
                border: none;
                border-radius: 6px;
                color: #ffffff;
                font-weight: 700;
                font-size: 14px;
                text-align: left;
                min-height: 20px;
                min-width: 140px;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                white-space: nowrap;
                overflow: visible;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.3 rgba(96, 165, 250, 0.7),
                    stop:0.7 rgba(139, 92, 246, 0.6),
                    stop:1 rgba(124, 58, 237, 0.7));
                color: #ffffff;
                font-weight: 900;
                border: 1px solid rgba(255, 255, 255, 0.4);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4),
                           0 0 15px rgba(96, 165, 250, 0.3),
                           inset 0 1px 0 rgba(255, 255, 255, 0.2);
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.3),
                    stop:0.5 rgba(139, 92, 246, 0.25),
                    stop:1 rgba(124, 58, 237, 0.3));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
            }
            QMenu::separator {
                height: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.5 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(255, 255, 255, 0.2));
                margin: 3px 8px;
                border: none;
                border-radius: 1px;
            }
            QMenu::indicator {
                width: 16px;
                height: 16px;
                margin-left: 0px;
                margin-right: 8px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: transparent;
                subcontrol-position: right center;
                subcontrol-origin: padding;
            }
            QMenu::indicator:checked {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(34, 197, 94, 0.9),
                    stop:1 rgba(22, 163, 74, 0.9));
                border: 2px solid rgba(34, 197, 94, 0.8);
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # قائمة الأعمدة مع أيقوناتها
        self.column_headers = [
            ("🔢 ID", 0),
            ("📋 رقم الفاتورة", 1),
            ("🧑‍💼 العميل", 2),
            ("💰 المبلغ", 3),
            ("📅 التاريخ", 4),
            ("📊 الحالة", 5),
            ("📝 الوصف", 6)
        ]

        # إضافة عناصر القائمة لكل عمود
        for header_text, column_index in self.column_headers:
            action = QAction(header_text, self)
            action.setCheckable(True)
            action.setChecked(True)  # جميع الأعمدة مرئية افتراضياً
            action.triggered.connect(lambda checked, col=column_index: self.toggle_column_visibility(col, checked))
            self.columns_menu.addAction(action)

        # إضافة فاصل
        self.columns_menu.addSeparator()

        # إضافة خيارات إضافية
        show_all_action = QAction("👁️ إظهار جميع الأعمدة", self)
        show_all_action.triggered.connect(self.show_all_columns)
        self.columns_menu.addAction(show_all_action)

        hide_all_action = QAction("🙈 إخفاء جميع الأعمدة", self)
        hide_all_action.triggered.connect(self.hide_all_columns)
        self.columns_menu.addAction(hide_all_action)

        # حفظ مرجع للقائمة
        columns_menu = self.columns_menu

        # تطبيق تصميم المحاذاة اليمنى للعناصر المحددة
        for action in columns_menu.actions():
            if action.text() and not action.isSeparator():
                # محاذاة النص لليمين مع مسافة كافية
                current_text = action.text()
                right_aligned_text = f"{current_text:>30}"
                action.setText(right_aligned_text)

        # تخصيص موضع وعرض القائمة
        def show_columns_menu():
            """عرض قائمة إدارة الأعمدة فوق الزر مباشرة بنفس العرض"""
            # الحصول على موضع الزر (فوق الزر)
            button_pos = self.columns_visibility_button.mapToGlobal(self.columns_visibility_button.rect().topLeft())

            # تحديد عرض القائمة لتكون مناسبة للنصوص
            button_width = self.columns_visibility_button.width()
            menu_width = max(button_width, 160)  # عرض أدنى 160 بكسل
            self.columns_menu.setFixedWidth(menu_width)

            # حساب ارتفاع القائمة لرفعها فوق الزر
            menu_height = self.columns_menu.sizeHint().height()
            button_pos.setY(button_pos.y() - menu_height)

            # عرض القائمة في الموضع المحدد
            self.columns_menu.exec_(button_pos)

        # ربط الزر بالدالة المخصصة
        self.columns_visibility_button.clicked.connect(show_columns_menu)

    def toggle_column_visibility(self, column_index, visible):
        """تبديل إظهار/إخفاء عمود محدد"""
        try:
            if hasattr(self, 'invoices_table') and self.invoices_table:
                if visible:
                    self.invoices_table.showColumn(column_index)
                else:
                    self.invoices_table.hideColumn(column_index)

                # تحديث حالة العنصر في القائمة
                for action in self.columns_menu.actions():
                    if action.data() == column_index:
                        action.setChecked(visible)
                        break

        except Exception as e:
            print(f"خطأ في تبديل إظهار العمود: {e}")

    def show_all_columns(self):
        """إظهار جميع الأعمدة"""
        try:
            if hasattr(self, 'invoices_table') and self.invoices_table:
                for i in range(self.invoices_table.columnCount()):
                    self.invoices_table.showColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(True)

        except Exception as e:
            print(f"خطأ في إظهار جميع الأعمدة: {e}")

    def hide_all_columns(self):
        """إخفاء جميع الأعمدة"""
        try:
            if hasattr(self, 'invoices_table') and self.invoices_table:
                for i in range(self.invoices_table.columnCount()):  # إخفاء جميع الأعمدة بما في ذلك ID
                    self.invoices_table.hideColumn(i)

                # تحديث حالة جميع العناصر في القائمة
                for action in self.columns_menu.actions():
                    if action.isCheckable():
                        action.setChecked(False)

        except Exception as e:
            print(f"خطأ في إخفاء الأعمدة: {e}")

    def style_simple_button(self, button, button_type):
        """تطبيق تصميم مبسط على الأزرار"""
        try:
            colors = {
                'primary': '#3b82f6',
                'emerald': '#10b981',
                'danger': '#ef4444',
                'info': '#0ea5e9',
                'modern_teal': '#14b8a6'
            }

            color = colors.get(button_type, '#6b7280')
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 10px 20px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'black': {
                    'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#2d2d2d', 'bg_bottom': '#404040',
                    'hover_start': '#2d2d2d', 'hover_mid': '#404040', 'hover_end': '#525252', 'hover_bottom': '#666666',
                    'hover_border': '#808080', 'pressed_start': '#000000', 'pressed_mid': '#000000',
                    'pressed_end': '#1a1a1a', 'pressed_bottom': '#2d2d2d', 'pressed_border': '#1a1a1a',
                    'border': '#404040', 'text': '#ffffff', 'shadow': 'rgba(102, 102, 102, 0.8)'
                }
            }

            # الحصول على ألوان الزر المحدد
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور والأنيق مع ألوان جديدة وحفظ المقاسات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)

        except Exception:
            pass

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة بدون مشاكل"""
        # إنشاء إطار للقائمة المخصصة
        self.status_filter_frame = QFrame()
        self.status_filter_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 6px 15px;
                min-width: 500px;
                max-width: 500px;
                min-height: 33px;
                max-height: 37px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                cursor: pointer;
            }
            QFrame:hover {
                border: 4px solid rgba(96, 165, 250, 0.9);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }
            QFrame:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.95),
                    stop:0.2 rgba(224, 242, 254, 0.9),
                    stop:0.4 rgba(186, 230, 253, 0.85),
                    stop:0.6 rgba(224, 242, 254, 0.9),
                    stop:0.8 rgba(240, 249, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                box-shadow: 0 4px 12px rgba(96, 165, 250, 0.3);
            }
        """)

        # تخطيط أفقي للإطار مع التوسيط العمودي
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(8, 0, 8, 0)  # إزالة الهوامش العمودية
        filter_layout.setSpacing(8)
        filter_layout.setAlignment(Qt.AlignVCenter)  # توسيط عمودي للعناصر

        # سهم يسار (مشابه للسهم الأيمن)
        self.left_arrow = QPushButton("▼")
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # النص الحالي
        self.current_filter_label = QLabel("جميع الحالات")
        self.current_filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص أفقياً وعمودياً
        self.current_filter_label.setStyleSheet("""
            QLabel {
                color: #1f2937;
                font-size: 16px;
                font-weight: 900;
                background: transparent;
                border: none;
                padding: 0px 12px;
                text-align: center;
                max-width: 435px;
                min-width: 435px;
                text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5);
                cursor: pointer;
            }
        """)

        # زر القائمة (سهم يمين)
        self.filter_menu_button = QPushButton("▼")
        self.filter_menu_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 2px solid rgba(96, 165, 250, 0.6);
                border-radius: 12px;
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
                min-width: 35px;
                max-width: 35px;
                min-height: 28px;
                max-height: 28px;
                padding: 0px;
                box-shadow:
                    0 2px 6px rgba(0, 0, 0, 0.15),
                    inset 0 1px 1px rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.8);
                transform: translateY(-1px) scale(1.05);
                box-shadow:
                    0 4px 12px rgba(0, 0, 0, 0.25),
                    0 0 15px rgba(96, 165, 250, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(29, 78, 216, 0.9),
                    stop:0.2 rgba(37, 99, 235, 0.8),
                    stop:0.4 rgba(59, 130, 246, 0.7),
                    stop:0.6 rgba(96, 165, 250, 0.8),
                    stop:0.8 rgba(124, 58, 237, 0.9),
                    stop:1 rgba(109, 40, 217, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(1px) scale(0.98);
                box-shadow:
                    0 0 15px rgba(96, 165, 250, 0.4),
                    0 2px 8px rgba(0, 0, 0, 0.4),
                    inset 0 1px 2px rgba(255, 255, 255, 0.3);
            }
        """)

        # إضافة العناصر للتخطيط - سهم يسار، النص في المنتصف، سهم يمين
        filter_layout.addWidget(self.left_arrow, 0)
        filter_layout.addWidget(self.current_filter_label, 1)
        filter_layout.addWidget(self.filter_menu_button, 0)

        self.status_filter_frame.setLayout(filter_layout)

        # إنشاء القائمة المنسدلة المخصصة
        self.create_filter_menu()

        # ربط الأزرار بالقائمة
        self.filter_menu_button.clicked.connect(self.show_filter_menu)
        self.left_arrow.clicked.connect(self.show_filter_menu)

        # إضافة ميزة الضغط على أي مكان في الإطار
        self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
        self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

        # جعل الإطار قابل للتركيز للحصول على تأثيرات أفضل
        self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        # تعيين القيمة الافتراضية
        self.current_filter_value = None

        # استخدام الإطار كـ status_filter
        self.status_filter = self.status_filter_frame

    def create_filter_menu(self):
        """إنشاء قائمة التصفية المخصصة"""
        self.filter_menu = QMenu(self)
        self.filter_menu.setStyleSheet("""
            QMenu {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 4px;
                padding: 8px;
                color: #1f2937;
                font-weight: 900;
                font-size: 16px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                min-width: 515px;
                max-width: 515px;
            }
            QMenu::item {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 2px solid rgba(96, 165, 250, 0.3);
                padding: 12px 0px;
                border-radius: 15px;
                margin: 3px;
                min-height: 32px;
                max-height: 32px;
                max-width: 495px;
                min-width: 495px;
                color: #1f2937;
                font-weight: 900;
                font-size: 17px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                text-align: center;
            }
            QMenu::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(96, 165, 250, 0.4),
                    stop:0.2 rgba(139, 92, 246, 0.3),
                    stop:0.4 rgba(124, 58, 237, 0.25),
                    stop:0.6 rgba(139, 92, 246, 0.3),
                    stop:0.8 rgba(96, 165, 250, 0.4),
                    stop:1 rgba(59, 130, 246, 0.35));
                border: 3px solid rgba(96, 165, 250, 0.7);
                color: #1f2937;
                font-weight: 900;
                box-shadow:
                    0 4px 12px rgba(96, 165, 250, 0.3),
                    0 0 15px rgba(96, 165, 250, 0.2),
                    inset 0 1px 2px rgba(255, 255, 255, 0.5);
                transform: scale(1.02);
            }
            QMenu::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(250, 251, 255, 0.95),
                    stop:0.2 rgba(241, 245, 249, 0.9),
                    stop:0.4 rgba(226, 232, 240, 0.85),
                    stop:0.6 rgba(241, 245, 249, 0.9),
                    stop:0.8 rgba(250, 251, 255, 0.95),
                    stop:1 rgba(255, 255, 255, 0.9));
                border: 4px solid rgba(96, 165, 250, 0.9);
                color: #1f2937;
                font-weight: 900;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                transform: translateY(-1px);
            }
            QMenu::separator {
                height: 1px;
                background: rgba(96, 165, 250, 0.2);
                margin: 3px 15px;
                border: none;
            }
        """)

        # إضافة العناصر مع أيقونات مطابقة للعملاء
        filter_options = [
            ("جميع الحالات", None),
            ("🟡 قيد الانتظار", "pending"),
            ("🟢 مدفوعة", "paid"),
            ("🔵 مدفوعة جزئيًا", "partially_paid"),
            ("🔴 ملغاة", "cancelled")
        ]

        for text, value in filter_options:
            # إنشاء عنصر مع توسيط النص المثالي
            centered_text = f"{text:^35}"  # توسيط النص في مساحة 35 حرف للعرض الجديد
            action = QAction(centered_text, self)
            action.setData(value)
            action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
            self.filter_menu.addAction(action)

    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_invoices()

    def export_excel_advanced(self):
        """تصدير Excel متقدم للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - Excel متقدم",
                f"فواتير_متقدم_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العناوين
                    headers = [
                        'الرقم', 'رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق',
                        'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة', 'الملاحظات'
                    ]
                    writer.writerow(headers)

                    # كتابة البيانات
                    for invoice in invoices:
                        remaining_amount = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                        writer.writerow([
                            invoice.id,
                            invoice.invoice_number,
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                            f"{invoice.total_amount:.2f}" if invoice.total_amount else "0.00",
                            f"{invoice.paid_amount:.2f}" if invoice.paid_amount else "0.00",
                            f"{remaining_amount:.2f}",
                            invoice.status or 'غير محدد',
                            invoice.notes or ''
                        ])

                self.show_success_message(f"تم تصدير الفواتير بنجاح (Excel متقدم):\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - CSV شامل",
                f"فواتير_شامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    headers = [
                        'الرقم', 'رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق',
                        'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة', 'الملاحظات'
                    ]
                    writer.writerow(headers)

                    for invoice in invoices:
                        remaining_amount = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                        writer.writerow([
                            invoice.id,
                            invoice.invoice_number,
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                            f"{invoice.total_amount:.2f}" if invoice.total_amount else "0.00",
                            f"{invoice.paid_amount:.2f}" if invoice.paid_amount else "0.00",
                            f"{remaining_amount:.2f}",
                            invoice.status or 'غير محدد',
                            invoice.notes or ''
                        ])

                self.show_success_message(f"تم تصدير الفواتير بنجاح (CSV شامل):\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير الشامل: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - PDF تفصيلي",
                f"فواتير_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()
                total_amount = sum(invoice.total_amount or 0 for invoice in invoices)
                total_paid = sum(invoice.paid_amount or 0 for invoice in invoices)
                total_remaining = total_amount - total_paid

                # إنشاء محتوى HTML
                html_content = f"""
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        .header {{ text-align: center; margin-bottom: 30px; }}
                        .summary {{ background-color: #f0f0f0; padding: 15px; margin-bottom: 20px; }}
                        table {{ width: 100%; border-collapse: collapse; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                        th {{ background-color: #4CAF50; color: white; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>تقرير الفواتير التفصيلي</h1>
                        <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>

                    <div class="summary">
                        <h3>ملخص الفواتير</h3>
                        <p>إجمالي عدد الفواتير: {len(invoices)}</p>
                        <p>إجمالي المبلغ: {total_amount:,.2f} جنيه</p>
                        <p>إجمالي المدفوع: {total_paid:,.2f} جنيه</p>
                        <p>إجمالي المتبقي: {total_remaining:,.2f} جنيه</p>
                    </div>

                    <table>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المبلغ المتبقي</th>
                            <th>الحالة</th>
                        </tr>
                """

                for invoice in invoices:
                    remaining = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                    status_text = {
                        'pending': 'معلقة',
                        'partially_paid': 'مدفوعة جزئياً',
                        'paid': 'مدفوعة بالكامل'
                    }.get(invoice.status, 'غير محدد')

                    html_content += f"""
                        <tr>
                            <td>{invoice.invoice_number or f'INV-{invoice.id}'}</td>
                            <td>{invoice.client.name if invoice.client else 'غير محدد'}</td>
                            <td>{invoice.date.strftime('%Y-%m-%d') if invoice.date else ''}</td>
                            <td>{invoice.total_amount:,.2f} جنيه</td>
                            <td>{invoice.paid_amount:,.2f} جنيه</td>
                            <td>{remaining:,.2f} جنيه</td>
                            <td>{status_text}</td>
                        </tr>
                    """

                html_content += """
                    </table>
                </body>
                </html>
                """

                # إنشاء PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير PDF التفصيلي بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF: {str(e)}")

    def export_monthly_report(self):
        """تصدير التقرير الشهري للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime, timedelta
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير الشهري",
                f"تقرير_شهري_فواتير_{datetime.now().strftime('%Y%m')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # الحصول على فواتير الشهر الحالي
                now = datetime.now()
                start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
                end_of_month = (start_of_month + timedelta(days=32)).replace(day=1) - timedelta(days=1)

                invoices = self.session.query(Invoice).filter(
                    Invoice.date >= start_of_month,
                    Invoice.date <= end_of_month
                ).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة معلومات التقرير
                    writer.writerow([f"التقرير الشهري للفواتير - {now.strftime('%Y/%m')}"])
                    writer.writerow([f"تاريخ التقرير: {now.strftime('%Y-%m-%d %H:%M:%S')}"])
                    writer.writerow([f"عدد الفواتير: {len(invoices)}"])
                    writer.writerow([])  # سطر فارغ

                    # العناوين
                    headers = ['رقم الفاتورة', 'العميل', 'التاريخ', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة']
                    writer.writerow(headers)

                    # البيانات
                    total_amount = 0
                    total_paid = 0
                    for invoice in invoices:
                        remaining = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                        total_amount += invoice.total_amount or 0
                        total_paid += invoice.paid_amount or 0

                        status_text = {
                            'pending': 'معلقة',
                            'partially_paid': 'مدفوعة جزئياً',
                            'paid': 'مدفوعة بالكامل'
                        }.get(invoice.status, 'غير محدد')

                        writer.writerow([
                            invoice.invoice_number or f'INV-{invoice.id}',
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            f"{invoice.total_amount:.2f}",
                            f"{invoice.paid_amount:.2f}",
                            f"{remaining:.2f}",
                            status_text
                        ])

                    # إضافة الإجماليات
                    writer.writerow([])
                    writer.writerow(['الإجماليات', '', '', f"{total_amount:.2f}", f"{total_paid:.2f}", f"{total_amount - total_paid:.2f}", ''])

                self.show_success_message(f"تم تصدير التقرير الشهري بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير التقرير الشهري: {str(e)}")

    def export_client_report(self):
        """تصدير تقرير العملاء"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تقرير العملاء",
                f"تقرير_عملاء_فواتير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # تجميع البيانات حسب العميل
                from sqlalchemy import func
                client_data = self.session.query(
                    Client.name,
                    func.count(Invoice.id).label('invoice_count'),
                    func.sum(Invoice.total_amount).label('total_amount'),
                    func.sum(Invoice.paid_amount).label('paid_amount')
                ).join(Invoice).group_by(Client.id, Client.name).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow([f"تقرير العملاء - الفواتير"])
                    writer.writerow([f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                    writer.writerow([])

                    # العناوين
                    headers = ['اسم العميل', 'عدد الفواتير', 'إجمالي المبلغ', 'إجمالي المدفوع', 'المبلغ المتبقي']
                    writer.writerow(headers)

                    # البيانات
                    for client_name, invoice_count, total_amount, paid_amount in client_data:
                        remaining = (total_amount or 0) - (paid_amount or 0)
                        writer.writerow([
                            client_name or 'غير محدد',
                            invoice_count or 0,
                            f"{total_amount or 0:.2f}",
                            f"{paid_amount or 0:.2f}",
                            f"{remaining:.2f}"
                        ])

                self.show_success_message(f"تم تصدير تقرير العملاء بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير تقرير العملاء: {str(e)}")

    def export_payment_report(self):
        """تصدير تقرير المدفوعات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تقرير المدفوعات",
                f"تقرير_مدفوعات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                # الحصول على الفواتير المدفوعة جزئياً أو كلياً
                invoices = self.session.query(Invoice).filter(
                    Invoice.paid_amount > 0
                ).order_by(Invoice.date.desc()).all()

                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # معلومات التقرير
                    writer.writerow([f"تقرير المدفوعات"])
                    writer.writerow([f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"])
                    writer.writerow([f"عدد الفواتير المدفوعة: {len(invoices)}"])
                    writer.writerow([])

                    # العناوين
                    headers = ['رقم الفاتورة', 'العميل', 'تاريخ الفاتورة', 'المبلغ الإجمالي', 'المبلغ المدفوع', 'نسبة الدفع', 'الحالة']
                    writer.writerow(headers)

                    # البيانات
                    total_invoiced = 0
                    total_paid = 0
                    for invoice in invoices:
                        payment_percentage = (invoice.paid_amount / invoice.total_amount * 100) if invoice.total_amount > 0 else 0
                        total_invoiced += invoice.total_amount or 0
                        total_paid += invoice.paid_amount or 0

                        status_text = {
                            'pending': 'معلقة',
                            'partially_paid': 'مدفوعة جزئياً',
                            'paid': 'مدفوعة بالكامل'
                        }.get(invoice.status, 'غير محدد')

                        writer.writerow([
                            invoice.invoice_number or f'INV-{invoice.id}',
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            f"{invoice.total_amount:.2f}",
                            f"{invoice.paid_amount:.2f}",
                            f"{payment_percentage:.1f}%",
                            status_text
                        ])

                    # إضافة الإجماليات
                    writer.writerow([])
                    overall_percentage = (total_paid / total_invoiced * 100) if total_invoiced > 0 else 0
                    writer.writerow(['الإجماليات', '', '', f"{total_invoiced:.2f}", f"{total_paid:.2f}", f"{overall_percentage:.1f}%", ''])

                self.show_success_message(f"تم تصدير تقرير المدفوعات بنجاح:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير تقرير المدفوعات: {str(e)}")

    def export_custom(self):
        """تصدير مخصص للفواتير مطابق تماماً لنافذة العملاء"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QPushButton, QLabel, QWidget

            # إنشاء نافذة مطابقة لنافذة الإحصائيات
            dialog = QDialog(self)
            dialog.setWindowTitle("📤 تصدير مخصص - الفواتير")
            dialog.setModal(True)
            dialog.resize(500, 650)

            # تطبيق نفس خلفية نافذة الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            # تخصيص شريط العنوان
            self.customize_title_bar_for_dialog(dialog)

            layout = QVBoxLayout(dialog)
            layout.setSpacing(8)
            layout.setContentsMargins(20, 15, 20, 15)

            # عنوان النافذة مطابق لنافذة الإحصائيات
            title_label = QLabel("📤 اختر البيانات المراد تصديرها")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 8px;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(title_label)

            # مجموعة البيانات الأساسية مطابقة لنافذة الإحصائيات
            basic_group = QWidget()
            basic_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            basic_main_layout = QVBoxLayout(basic_group)
            basic_main_layout.setSpacing(5)
            basic_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            basic_title = QLabel("📋 البيانات الأساسية")
            basic_title.setAlignment(Qt.AlignCenter)
            basic_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            basic_main_layout.addWidget(basic_title)

            basic_layout = QVBoxLayout()
            basic_layout.setSpacing(3)

            self.export_invoice_number = QCheckBox("🧾 رقم الفاتورة")
            self.export_client_name = QCheckBox("👤 اسم العميل")
            self.export_date = QCheckBox("📅 تاريخ الفاتورة")
            self.export_due_date = QCheckBox("⏰ تاريخ الاستحقاق")

            # تحديد افتراضي
            self.export_invoice_number.setChecked(True)
            self.export_client_name.setChecked(True)
            self.export_date.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات
            basic_checkboxes_data = [
                (self.export_invoice_number, "#3B82F6"),
                (self.export_client_name, "#10B981"),
                (self.export_date, "#F59E0B"),
                (self.export_due_date, "#EF4444")
            ]

            for checkbox, color in basic_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 2px;
                        margin: 0px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(8)
                item_layout.setContentsMargins(8, 3, 8, 3)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                checkbox.stateChanged.connect(lambda state, label=check_label: label.setText("✓") if state == 2 else label.setText(""))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                basic_layout.addWidget(item_widget)

            basic_main_layout.addLayout(basic_layout)
            layout.addWidget(basic_group)

            # مجموعة البيانات المالية مطابقة لنافذة الإحصائيات
            financial_group = QWidget()
            financial_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            financial_main_layout = QVBoxLayout(financial_group)
            financial_main_layout.setSpacing(5)
            financial_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            financial_title = QLabel("💰 البيانات المالية")
            financial_title.setAlignment(Qt.AlignCenter)
            financial_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            financial_main_layout.addWidget(financial_title)

            financial_layout = QVBoxLayout()
            financial_layout.setSpacing(3)

            self.export_total_amount = QCheckBox("💵 المبلغ الإجمالي")
            self.export_paid_amount = QCheckBox("💳 المبلغ المدفوع")
            self.export_remaining_amount = QCheckBox("📊 المبلغ المتبقي")

            self.export_total_amount.setChecked(True)
            self.export_paid_amount.setChecked(True)

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات المالية
            financial_checkboxes_data = [
                (self.export_total_amount, "#10B981"),
                (self.export_paid_amount, "#F59E0B"),
                (self.export_remaining_amount, "#EF4444")
            ]

            for checkbox, color in financial_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                checkbox.stateChanged.connect(lambda state, label=check_label: label.setText("✓") if state == 2 else label.setText(""))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                financial_layout.addWidget(item_widget)

            financial_main_layout.addLayout(financial_layout)
            layout.addWidget(financial_group)

            # مجموعة البيانات الإضافية مطابقة لنافذة الإحصائيات
            additional_group = QWidget()
            additional_group.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;
                    margin: 1px;
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            additional_main_layout = QVBoxLayout(additional_group)
            additional_main_layout.setSpacing(5)
            additional_main_layout.setContentsMargins(15, 5, 15, 5)

            # عنوان المجموعة مضغوط ومتوسط
            additional_title = QLabel("📝 البيانات الإضافية")
            additional_title.setAlignment(Qt.AlignCenter)
            additional_title.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 2px;
                    margin-bottom: 5px;
                }
            """)
            additional_main_layout.addWidget(additional_title)

            additional_layout = QVBoxLayout()
            additional_layout.setSpacing(3)

            self.export_status = QCheckBox("📊 حالة الفاتورة")
            self.export_notes = QCheckBox("📋 الملاحظات")
            self.export_statistics = QCheckBox("📈 إضافة الإحصائيات")

            # تطبيق تصميم مطابق لعناصر الإحصائيات للبيانات الإضافية
            additional_checkboxes_data = [
                (self.export_status, "#8B5CF6"),
                (self.export_notes, "#F59E0B"),
                (self.export_statistics, "#3B82F6")
            ]

            for checkbox, color in additional_checkboxes_data:
                # إنشاء عنصر مطابق لعناصر الإحصائيات
                item_widget = QWidget()
                item_widget.setStyleSheet("""
                    QWidget {
                        background: transparent;
                        padding: 5px;
                        margin: 1px;
                    }
                    QWidget:hover {
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 6px;
                    }
                """)

                item_layout = QHBoxLayout(item_widget)
                item_layout.setSpacing(12)
                item_layout.setContentsMargins(12, 5, 12, 5)

                # تصميم الـ CheckBox مع علامة صح واضحة
                checkbox.setStyleSheet(f"""
                    QCheckBox {{
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: normal;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        spacing: 12px;
                    }}
                    QCheckBox::indicator {{
                        width: 20px;
                        height: 20px;
                        border: 2px solid {color};
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }}
                    QCheckBox::indicator:checked {{
                        background: {color};
                        border: 2px solid #ffffff;
                        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgMTBMOCAxNEwxNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
                    }}
                    QCheckBox::indicator:hover {{
                        background: rgba(255, 255, 255, 0.2);
                        border: 2px solid rgba(255, 255, 255, 0.8);
                    }}
                """)

                # إضافة علامة صح خارجية محسنة
                check_label = QLabel("")
                check_label.setFixedSize(30, 25)
                check_label.setAlignment(Qt.AlignCenter)
                check_label.setStyleSheet("""
                    QLabel {
                        color: #10B981;
                        font-size: 20px;
                        font-weight: bold;
                        background: rgba(16, 185, 129, 0.1);
                        border: 1px solid rgba(16, 185, 129, 0.3);
                        border-radius: 8px;
                        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                        padding: 2px;
                    }
                """)

                # ربط تغيير حالة الـ CheckBox بإظهار/إخفاء علامة الصح
                checkbox.stateChanged.connect(lambda state, label=check_label: label.setText("✓") if state == 2 else label.setText(""))

                # إظهار علامة الصح للعناصر المحددة مسبقاً
                if checkbox.isChecked():
                    check_label.setText("✓")

                item_layout.addWidget(check_label)  # علامة الصح أولاً (الجانب الأيمن)
                item_layout.addWidget(checkbox)
                additional_layout.addWidget(item_widget)

            additional_main_layout.addLayout(additional_layout)
            layout.addWidget(additional_group)

            # أزرار التحكم مطابقة لنافذة الإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            # زر الإلغاء مطابق للبرنامج الرئيسي
            cancel_btn = QPushButton("❌ إلغاء")
            cancel_btn.clicked.connect(dialog.reject)
            cancel_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(cancel_btn, 'danger')

            # زر التصدير مطابق للبرنامج الرئيسي
            export_btn = QPushButton("📤 تصدير")
            export_btn.clicked.connect(lambda: self.perform_custom_export_invoices(dialog))
            export_btn.setMinimumHeight(45)

            # استخدام دالة التصميم من البرنامج الرئيسي
            self.style_advanced_button(export_btn, 'emerald')

            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addWidget(export_btn)
            layout.addLayout(buttons_layout)

            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def customize_title_bar_for_dialog(self, dialog):
        """تخصيص شريط العنوان للنافذة"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(dialog)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def perform_custom_export_invoices(self, dialog):
        """تنفيذ التصدير المخصص للفواتير مطابق للعملاء"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ التصدير المخصص", f"تصدير_مخصص_فواتير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إضافة الإحصائيات إذا تم اختيارها
                    if self.export_statistics.isChecked():
                        writer.writerow(['تصدير مخصص للفواتير'])
                        writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                        writer.writerow([f'إجمالي الفواتير: {len(invoices)}'])

                        # إحصائيات إضافية
                        total_amount = sum(invoice.total_amount or 0 for invoice in invoices)
                        total_paid = sum(invoice.paid_amount or 0 for invoice in invoices)
                        total_remaining = total_amount - total_paid

                        writer.writerow([f'إجمالي المبلغ: {total_amount:,.2f} جنيه'])
                        writer.writerow([f'إجمالي المدفوع: {total_paid:,.2f} جنيه'])
                        writer.writerow([f'إجمالي المتبقي: {total_remaining:,.2f} جنيه'])
                        writer.writerow([])

                    # إنشاء رؤوس الأعمدة حسب الاختيار
                    headers = []
                    if self.export_invoice_number.isChecked():
                        headers.append('رقم الفاتورة')
                    if self.export_client_name.isChecked():
                        headers.append('اسم العميل')
                    if self.export_date.isChecked():
                        headers.append('تاريخ الفاتورة')
                    if self.export_due_date.isChecked():
                        headers.append('تاريخ الاستحقاق')
                    if self.export_total_amount.isChecked():
                        headers.append('المبلغ الإجمالي')
                    if self.export_paid_amount.isChecked():
                        headers.append('المبلغ المدفوع')
                    if self.export_remaining_amount.isChecked():
                        headers.append('المبلغ المتبقي')
                    if self.export_status.isChecked():
                        headers.append('حالة الفاتورة')
                    if self.export_notes.isChecked():
                        headers.append('الملاحظات')

                    writer.writerow(headers)

                    # كتابة البيانات
                    for invoice in invoices:
                        row = []
                        if self.export_invoice_number.isChecked():
                            row.append(invoice.invoice_number or f'INV-{invoice.id}')
                        if self.export_client_name.isChecked():
                            row.append(invoice.client.name if invoice.client else 'غير محدد')
                        if self.export_date.isChecked():
                            row.append(invoice.date.strftime('%Y-%m-%d') if invoice.date else 'غير محدد')
                        if self.export_due_date.isChecked():
                            row.append(invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else 'غير محدد')
                        if self.export_total_amount.isChecked():
                            row.append(f"{invoice.total_amount:.2f}" if invoice.total_amount else "0.00")
                        if self.export_paid_amount.isChecked():
                            row.append(f"{invoice.paid_amount:.2f}" if invoice.paid_amount else "0.00")
                        if self.export_remaining_amount.isChecked():
                            remaining = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                            row.append(f"{remaining:.2f}")
                        if self.export_status.isChecked():
                            status_text = {
                                'pending': 'معلقة',
                                'partially_paid': 'مدفوعة جزئياً',
                                'paid': 'مدفوعة بالكامل'
                            }.get(invoice.status, 'غير محدد')
                            row.append(status_text)
                        if self.export_notes.isChecked():
                            row.append(invoice.notes or 'لا توجد ملاحظات')

                        writer.writerow(row)

                dialog.accept()
                self.show_success_message(f"تم التصدير المخصص بنجاح إلى:\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_فواتير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(invoices),
                        'backup_type': 'invoices_full_backup',
                        'version': '1.0'
                    },
                    'invoices': []
                }

                for invoice in invoices:
                    invoice_data = {
                        'id': invoice.id,
                        'invoice_number': invoice.invoice_number,
                        'client_id': invoice.client_id,
                        'client_name': invoice.client.name if invoice.client else None,
                        'date': invoice.date.isoformat() if invoice.date else None,
                        'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                        'total_amount': float(invoice.total_amount) if invoice.total_amount else 0.0,
                        'paid_amount': float(invoice.paid_amount) if invoice.paid_amount else 0.0,
                        'status': invoice.status,
                        'notes': invoice.notes,
                        'items': []
                    }

                    # إضافة عناصر الفاتورة
                    for item in invoice.items:
                        item_data = {
                            'description': item.description,
                            'quantity': float(item.quantity) if item.quantity else 0.0,
                            'unit_price': float(item.unit_price) if item.unit_price else 0.0,
                            'total_price': float(item.total_price) if item.total_price else 0.0
                        }
                        invoice_data['items'].append(item_data)

                    backup_data['invoices'].append(invoice_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(invoices)} فاتورة")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للفواتير"""
        self.show_info_message("ميزة استعادة النسخة الاحتياطية قيد التطوير")


class InvoicesStatisticsDialog(QDialog):
    """نافذة إحصائيات الفواتير مطابقة للعملاء والموردين والعمال والمشاريع والعقارات والمخزون والمبيعات والمشتريات والمصروفات والإيرادات"""

    def __init__(self, session, parent=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة النافذة المتطورة"""
        # إعداد النافذة الأساسي مع شريط عنوان موحد
        self.setWindowTitle("📊 إحصائيات الفواتير - نظام إدارة العملاء المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(600, 420)  # نفس ارتفاع نافذة العملاء

        # تخصيص شريط العنوان الموحد
        self.customize_title_bar()

        # تطبيق نمط النافذة
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: none;
                border-radius: 15px;
            }
        """)

        # التخطيط الرئيسي المضغوط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)  # تقليل الهوامش
        layout.setSpacing(12)  # تقليل المسافات من 20 إلى 12

        # العنوان الرئيسي المطور بدون إطار - مطابق للعملاء
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background: transparent;
                padding: 10px;
            }
        """)

        title_inner_layout = QVBoxLayout(title_container)
        title_inner_layout.setContentsMargins(0, 0, 0, 0)
        title_inner_layout.setSpacing(8)

        # الأيقونة والعنوان الرئيسي
        main_title = QLabel("📊 إحصائيات الفواتير")
        main_title.setAlignment(Qt.AlignCenter)
        main_title.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 30px;
                font-weight: bold;
                background: transparent;
                text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.9);
                padding: 10px;
            }
        """)

        # العنوان الفرعي التوضيحي
        subtitle = QLabel("تقرير شامل عن حالة الفواتير والمدفوعات")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                font-weight: normal;
                background: transparent;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 5px;
            }
        """)

        title_inner_layout.addWidget(main_title)
        title_inner_layout.addWidget(subtitle)
        layout.addWidget(title_container)

        # حساب الإحصائيات
        self.calculate_statistics()

        # إنشاء قائمة الإحصائيات المضغوطة
        stats_layout = QVBoxLayout()
        stats_layout.setSpacing(8)  # تقليل المسافة أكثر من 15 إلى 8
        stats_layout.setContentsMargins(20, 10, 20, 10)  # تقليل الهوامش أكثر

        # إنشاء قائمة الإحصائيات
        stats_items = [
            ("🧾", "إجمالي الفواتير المسجلة", str(self.total_invoices), "#3B82F6"),
            ("✅", "الفواتير المدفوعة بالكامل", str(self.paid_invoices), "#10B981"),
            ("🔄", "الفواتير المدفوعة جزئياً", str(self.partially_paid_invoices), "#F59E0B"),
            ("⏳", "الفواتير المعلقة والمؤجلة", str(self.pending_invoices), "#EF4444"),
            ("💰", "إجمالي قيمة الفواتير", format_currency(self.total_amount), "#10B981")
        ]

        for icon, title, value, color in stats_items:
            # إنشاء عنصر مضغوط بدون إطارات
            item_widget = QWidget()
            item_widget.setStyleSheet("""
                QWidget {
                    background: transparent;
                    padding: 5px;  /* تقليل من 8px إلى 5px */
                    margin: 1px;   /* تقليل من 2px إلى 1px */
                }
                QWidget:hover {
                    background: rgba(255, 255, 255, 0.05);
                    border-radius: 6px;
                }
            """)

            item_layout = QHBoxLayout(item_widget)
            item_layout.setSpacing(12)  # تقليل من 15 إلى 12
            item_layout.setContentsMargins(12, 5, 12, 5)  # تقليل الهوامش

            # الأيقونة بدون إطارات
            icon_label = QLabel(icon)
            icon_label.setFixedSize(28, 28)  # تصغير أكثر
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;  /* تصغير أكثر */
                    font-weight: bold;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 5px;
                }}
            """)
            item_layout.addWidget(icon_label)

            # العنوان المطور مع وصف مفصل
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    padding: 4px 8px;
                }
            """)
            item_layout.addWidget(title_label)

            # مساحة فارغة للدفع
            item_layout.addStretch()

            # القيمة بدون إطارات
            value_label = QLabel(value)
            value_label.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 18px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                    background: transparent;
                    border: none;
                    padding: 6px 12px;
                    min-width: 70px;
                }}
            """)
            value_label.setAlignment(Qt.AlignCenter)
            item_layout.addWidget(value_label)

            stats_layout.addWidget(item_widget)

        layout.addLayout(stats_layout)

        # أزرار التحكم مطابقة للعملاء
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # زر تصدير PDF
        export_pdf_button = QPushButton("📄 تصدير PDF")
        export_pdf_button.clicked.connect(self.export_statistics_to_pdf)
        export_pdf_button.setMinimumHeight(45)
        self.style_advanced_button(export_pdf_button, 'info')

        # زر الإغلاق
        close_button = QPushButton("❌ إغلاق")
        close_button.clicked.connect(self.accept)
        close_button.setMinimumHeight(45)
        self.style_advanced_button(close_button, 'danger')

        buttons_layout.addWidget(close_button)
        buttons_layout.addWidget(export_pdf_button)

        layout.addLayout(buttons_layout)

    def calculate_statistics(self):
        """حساب إحصائيات الفواتير"""
        try:
            # حساب إجمالي الفواتير
            self.total_invoices = self.session.query(Invoice).count()

            # حساب الفواتير حسب الحالة
            self.paid_invoices = self.session.query(Invoice).filter(Invoice.status == 'paid').count()
            self.partially_paid_invoices = self.session.query(Invoice).filter(Invoice.status == 'partially_paid').count()
            self.pending_invoices = self.session.query(Invoice).filter(Invoice.status == 'pending').count()

            # حساب إجمالي المبلغ
            total_amount_result = self.session.query(func.sum(Invoice.total_amount)).scalar()
            self.total_amount = total_amount_result or 0

        except Exception as e:
            print(f"خطأ في حساب إحصائيات الفواتير: {e}")
            self.total_invoices = 0
            self.paid_invoices = 0
            self.partially_paid_invoices = 0
            self.pending_invoices = 0
            self.total_amount = 0

    def export_statistics_to_pdf(self):
        """تصدير الإحصائيات إلى PDF - دالة مؤقتة"""
        QMessageBox.information(self, "تصدير PDF", "ميزة تصدير PDF قيد التطوير")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور للأزرار مطابق للعملاء"""
        try:
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للشريط الرئيسي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: #ffffff;
                        border: 2px solid rgba(255, 255, 255, 0.2);
                        border-radius: 12px;
                        padding: 12px 24px;
                        font-size: 14px;
                        font-weight: bold;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
                        min-width: 120px;
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['base']});
                        border: 3px solid rgba(255, 255, 255, 0.4);
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 {colors['pressed']}, stop:1 {colors['hover']});
                        transform: translateY(0px);
                        box-shadow: 0 4px 15px {colors['shadow']};
                    }}
                """)

        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def customize_title_bar(self):
        """تخصيص شريط العنوان"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان: {e}")

    # دوال التصدير المتقدمة الجديدة - مطابقة للعملاء والمصروفات
    def export_excel_advanced(self):
        """تصدير Excel متقدم للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - Excel متقدم",
                f"فواتير_متقدم_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة العنوان
                    writer.writerow(['تقرير الفواتير المتقدم'])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي الفواتير: {len(invoices)}'])
                    writer.writerow([])  # سطر فارغ

                    # كتابة الرؤوس
                    writer.writerow([
                        'الرقم', 'رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق',
                        'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة', 'الملاحظات'
                    ])

                    # كتابة البيانات
                    for invoice in invoices:
                        remaining_amount = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                        writer.writerow([
                            invoice.id,
                            invoice.invoice_number,
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                            f"{invoice.total_amount:.2f}" if invoice.total_amount else "0.00",
                            f"{invoice.paid_amount:.2f}" if invoice.paid_amount else "0.00",
                            f"{remaining_amount:.2f}",
                            invoice.status or 'غير محدد',
                            invoice.notes or ''
                        ])

                self.show_success_message(f"تم تصدير الفواتير بنجاح (Excel متقدم):\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير المتقدم: {str(e)}")

    def export_csv_advanced(self):
        """تصدير CSV شامل للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import csv

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - CSV شامل",
                f"فواتير_شامل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()

                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # إحصائيات شاملة
                    total_amount = sum(invoice.total_amount or 0 for invoice in invoices)
                    total_paid = sum(invoice.paid_amount or 0 for invoice in invoices)
                    total_remaining = total_amount - total_paid

                    # إحصائيات الحالات
                    status_stats = {}
                    for invoice in invoices:
                        status = invoice.status or 'غير محدد'
                        if status in status_stats:
                            status_stats[status] += 1
                        else:
                            status_stats[status] = 1

                    # كتابة الإحصائيات
                    writer.writerow(['=== تقرير الفواتير الشامل ==='])
                    writer.writerow([f'تاريخ التصدير: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'إجمالي عدد الفواتير: {len(invoices)}'])
                    writer.writerow([f'إجمالي المبلغ: {total_amount:.2f} جنيه'])
                    writer.writerow([f'إجمالي المدفوع: {total_paid:.2f} جنيه'])
                    writer.writerow([f'إجمالي المتبقي: {total_remaining:.2f} جنيه'])
                    writer.writerow([])

                    # إحصائيات الحالات
                    writer.writerow(['=== إحصائيات الحالات ==='])
                    for status, count in status_stats.items():
                        writer.writerow([f'{status}: {count} فاتورة'])
                    writer.writerow([])

                    # البيانات التفصيلية
                    writer.writerow(['=== البيانات التفصيلية ==='])
                    writer.writerow([
                        'الرقم', 'رقم الفاتورة', 'العميل', 'التاريخ', 'تاريخ الاستحقاق',
                        'المبلغ الإجمالي', 'المبلغ المدفوع', 'المبلغ المتبقي', 'الحالة', 'الملاحظات'
                    ])

                    for invoice in invoices:
                        remaining_amount = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                        writer.writerow([
                            invoice.id,
                            invoice.invoice_number,
                            invoice.client.name if invoice.client else 'غير محدد',
                            invoice.date.strftime('%Y-%m-%d') if invoice.date else '',
                            invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else '',
                            f"{invoice.total_amount:.2f}" if invoice.total_amount else "0.00",
                            f"{invoice.paid_amount:.2f}" if invoice.paid_amount else "0.00",
                            f"{remaining_amount:.2f}",
                            invoice.status or 'غير محدد',
                            invoice.notes or ''
                        ])

                self.show_success_message(f"تم تصدير الفواتير بنجاح (CSV شامل):\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في التصدير الشامل: {str(e)}")

    def export_pdf_advanced(self):
        """تصدير PDF تفصيلي للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QTextDocument
            from datetime import datetime

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير الفواتير - PDF تفصيلي",
                f"فواتير_تفصيلي_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()
                total_amount = sum(invoice.total_amount or 0 for invoice in invoices)
                total_paid = sum(invoice.paid_amount or 0 for invoice in invoices)
                total_remaining = total_amount - total_paid

                # إنشاء محتوى HTML متقدم
                html_content = f"""
                <html dir="rtl">
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 20px; }}
                        .header {{ text-align: center; color: #2563EB; margin-bottom: 30px; }}
                        .summary {{ background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 20px; }}
                        table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                        th {{ background-color: #2563EB; color: white; }}
                        .total {{ font-weight: bold; background-color: #e3f2fd; }}
                        .paid {{ color: #10b981; }}
                        .remaining {{ color: #ef4444; }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>📋 تقرير الفواتير التفصيلي</h1>
                        <p>تاريخ التصدير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>

                    <div class="summary">
                        <h3>📈 ملخص الإحصائيات</h3>
                        <p><strong>إجمالي عدد الفواتير:</strong> {len(invoices)}</p>
                        <p><strong>إجمالي المبلغ:</strong> {total_amount:.2f} جنيه</p>
                        <p><strong>إجمالي المدفوع:</strong> <span class="paid">{total_paid:.2f} جنيه</span></p>
                        <p><strong>إجمالي المتبقي:</strong> <span class="remaining">{total_remaining:.2f} جنيه</span></p>
                        <p><strong>متوسط قيمة الفاتورة:</strong> {(total_amount/len(invoices)):.2f} جنيه</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ المدفوع</th>
                                <th>المبلغ المتبقي</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                """

                for invoice in invoices:
                    remaining_amount = (invoice.total_amount or 0) - (invoice.paid_amount or 0)
                    html_content += f"""
                        <tr>
                            <td>{invoice.invoice_number}</td>
                            <td>{invoice.client.name if invoice.client else 'غير محدد'}</td>
                            <td>{invoice.date.strftime('%Y-%m-%d') if invoice.date else ''}</td>
                            <td>{invoice.total_amount:.2f} جنيه</td>
                            <td class="paid">{invoice.paid_amount:.2f} جنيه</td>
                            <td class="remaining">{remaining_amount:.2f} جنيه</td>
                            <td>{invoice.status or 'غير محدد'}</td>
                        </tr>
                    """

                html_content += f"""
                        </tbody>
                        <tfoot>
                            <tr class="total">
                                <td colspan="3"><strong>الإجمالي</strong></td>
                                <td><strong>{total_amount:.2f} جنيه</strong></td>
                                <td><strong>{total_paid:.2f} جنيه</strong></td>
                                <td><strong>{total_remaining:.2f} جنيه</strong></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </body>
                </html>
                """

                # إنشاء PDF
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPrinter.A4)

                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                self.show_success_message(f"تم تصدير الفواتير بنجاح (PDF تفصيلي):\n{file_path}")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تصدير PDF التفصيلي: {str(e)}")

    def export_monthly_report(self):
        """تصدير التقرير الشهري للفواتير"""
        self.show_info_message("ميزة التقرير الشهري قيد التطوير")

    def export_client_report(self):
        """تصدير تقرير العملاء"""
        self.show_info_message("ميزة تقرير العملاء قيد التطوير")

    def export_payment_report(self):
        """تصدير تقرير المدفوعات"""
        self.show_info_message("ميزة تقرير المدفوعات قيد التطوير")

    def export_custom(self):
        """تصدير مخصص للفواتير مع خيارات متقدمة - مطابق تماماً للعملاء والمصروفات"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QCheckBox, QGroupBox, QPushButton, QLabel

            # إنشاء نافذة الخيارات المخصصة مع ألوان الإحصائيات - مطابق للعملاء
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 تصدير مخصص - خيارات متقدمة")
            dialog.setModal(True)
            dialog.resize(450, 500)

            # تطبيق نفس تصميم نوافذ الإحصائيات
            dialog.setStyleSheet("""
                QDialog {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                        stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                        stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                        stop:0.9 #6D28D9, stop:1 #5B21B6);
                    border: none;
                    border-radius: 15px;
                }
            """)

            layout = QVBoxLayout()
            layout.setContentsMargins(25, 25, 25, 25)
            layout.setSpacing(20)

            # عنوان النافذة مطابق للإحصائيات
            title_label = QLabel("🔧 تصدير مخصص للفواتير")
            title_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 30px;
                    font-weight: bold;
                    text-align: center;
                    padding: 15px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.5 rgba(59, 130, 246, 0.9),
                        stop:1 rgba(96, 165, 250, 0.8));
                    border: 3px solid rgba(37, 99, 235, 0.6);
                    border-radius: 12px;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
                }
            """)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)

            # عنوان فرعي
            subtitle_label = QLabel("اختر البيانات المراد تصديرها")
            subtitle_label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    padding: 8px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                }
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(subtitle_label)

            # مجموعة خيارات البيانات
            data_group = QGroupBox("📊 البيانات المراد تصديرها")
            data_group.setStyleSheet("""
                QGroupBox {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(255, 255, 255, 0.1);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                }
            """)
            data_layout = QVBoxLayout()

            # خيارات التصدير مع علامات خارجية
            self.export_basic_info = QCheckBox("✓ المعلومات الأساسية")
            self.export_amounts = QCheckBox("✓ المبالغ والمدفوعات")
            self.export_dates = QCheckBox("✓ التواريخ والاستحقاق")
            self.export_clients = QCheckBox("✓ معلومات العملاء")
            self.export_status = QCheckBox("✓ الحالات والملاحظات")
            self.export_statistics = QCheckBox("✓ الإحصائيات والتحليلات")

            # تحديد جميع الخيارات افتراضياً
            for checkbox in [self.export_basic_info, self.export_amounts, self.export_dates,
                           self.export_clients, self.export_status, self.export_statistics]:
                checkbox.setChecked(True)
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #ffffff;
                        font-size: 13px;
                        font-weight: bold;
                        padding: 5px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #60A5FA;
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }
                    QCheckBox::indicator:checked {
                        background: #60A5FA;
                        border: 2px solid #3B82F6;
                    }
                """)
                data_layout.addWidget(checkbox)

            data_group.setLayout(data_layout)
            layout.addWidget(data_group)

            # مجموعة تنسيق التصدير
            format_group = QGroupBox("📄 تنسيق التصدير")
            format_group.setStyleSheet("""
                QGroupBox {
                    color: #ffffff;
                    font-size: 14px;
                    font-weight: bold;
                    border: 2px solid rgba(255, 255, 255, 0.3);
                    border-radius: 10px;
                    margin-top: 10px;
                    padding-top: 10px;
                    background: rgba(255, 255, 255, 0.1);
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 10px 0 10px;
                }
            """)
            format_layout = QVBoxLayout()

            self.format_excel = QCheckBox("✓ Excel (.xlsx)")
            self.format_csv = QCheckBox("✓ CSV (.csv)")
            self.format_pdf = QCheckBox("✓ PDF (.pdf)")
            self.format_json = QCheckBox("✓ JSON (.json)")

            # تحديد Excel افتراضياً
            self.format_excel.setChecked(True)

            for checkbox in [self.format_excel, self.format_csv, self.format_pdf, self.format_json]:
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #ffffff;
                        font-size: 13px;
                        font-weight: bold;
                        padding: 5px;
                        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    }
                    QCheckBox::indicator {
                        width: 18px;
                        height: 18px;
                        border: 2px solid #10B981;
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.1);
                    }
                    QCheckBox::indicator:checked {
                        background: #10B981;
                        border: 2px solid #059669;
                    }
                """)
                format_layout.addWidget(checkbox)

            format_group.setLayout(format_layout)
            layout.addWidget(format_group)

            # أزرار التحكم مطابقة للإحصائيات
            buttons_layout = QHBoxLayout()
            buttons_layout.setSpacing(15)

            cancel_button = QPushButton("❌ إلغاء")
            cancel_button.clicked.connect(dialog.reject)
            cancel_button.setMinimumHeight(45)
            self.style_advanced_button(cancel_button, 'danger')

            export_button = QPushButton("📤 تصدير")
            export_button.clicked.connect(lambda: self.perform_custom_export(dialog))
            export_button.setMinimumHeight(45)
            self.style_advanced_button(export_button, 'emerald')

            buttons_layout.addWidget(cancel_button)
            buttons_layout.addWidget(export_button)
            layout.addLayout(buttons_layout)

            dialog.setLayout(layout)
            dialog.exec_()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في نافذة التصدير المخصص: {str(e)}")

    def perform_custom_export(self, dialog):
        """تنفيذ التصدير المخصص"""
        try:
            # التحقق من اختيار تنسيق واحد على الأقل
            if not any([self.format_excel.isChecked(), self.format_csv.isChecked(),
                       self.format_pdf.isChecked(), self.format_json.isChecked()]):
                show_invoice_advanced_error(self, "خطأ", "يجب اختيار تنسيق واحد على الأقل للتصدير")
                return

            dialog.accept()

            # تنفيذ التصدير حسب التنسيقات المختارة
            if self.format_excel.isChecked():
                self.export_excel_advanced()

            if self.format_csv.isChecked():
                self.export_csv_advanced()

            if self.format_pdf.isChecked():
                self.export_pdf_advanced()

            if self.format_json.isChecked():
                self.export_to_json()

        except Exception as e:
            self.show_error_message(f"حدث خطأ في تنفيذ التصدير المخصص: {str(e)}")

    def export_backup(self):
        """إنشاء نسخة احتياطية شاملة للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime
            import json

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_فواتير_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json)"
            )

            if file_path:
                invoices = self.session.query(Invoice).all()
                backup_data = {
                    'backup_info': {
                        'created_at': datetime.now().isoformat(),
                        'total_records': len(invoices),
                        'backup_type': 'invoices_full_backup',
                        'version': '1.0'
                    },
                    'invoices': []
                }

                for invoice in invoices:
                    invoice_data = {
                        'id': invoice.id,
                        'invoice_number': invoice.invoice_number,
                        'client_id': invoice.client_id,
                        'client_name': invoice.client.name if invoice.client else None,
                        'date': invoice.date.isoformat() if invoice.date else None,
                        'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                        'total_amount': float(invoice.total_amount) if invoice.total_amount else 0.0,
                        'paid_amount': float(invoice.paid_amount) if invoice.paid_amount else 0.0,
                        'status': invoice.status,
                        'notes': invoice.notes,
                        'items': []
                    }

                    # إضافة عناصر الفاتورة
                    if invoice.items:
                        for item in invoice.items:
                            item_data = {
                                'description': item.description,
                                'quantity': float(item.quantity) if item.quantity else 0.0,
                                'unit_price': float(item.unit_price) if item.unit_price else 0.0,
                                'total_price': float(item.total_price) if item.total_price else 0.0
                            }
                            invoice_data['items'].append(item_data)

                    backup_data['invoices'].append(invoice_data)

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                self.show_success_message(f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}\n\nتم حفظ {len(invoices)} فاتورة")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية للفواتير"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import json
            from datetime import datetime

            file_path, _ = QFileDialog.getOpenFileName(
                self, "اختر النسخة الاحتياطية", "", "JSON Files (*.json)"
            )

            if file_path:
                # تأكيد الاستعادة
                reply = QMessageBox.question(
                    self, "تأكيد الاستعادة",
                    "هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\nسيتم دمج البيانات مع الموجودة حالياً!",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    if 'invoices' in backup_data:
                        restored_count = 0
                        updated_count = 0

                        for invoice_data in backup_data['invoices']:
                            # التحقق من وجود الفاتورة
                            existing = self.session.query(Invoice).filter_by(invoice_number=invoice_data.get('invoice_number')).first()

                            if existing:
                                # تحديث الفاتورة الموجودة
                                existing.total_amount = invoice_data.get('total_amount', 0)
                                existing.paid_amount = invoice_data.get('paid_amount', 0)
                                existing.status = invoice_data.get('status')
                                existing.notes = invoice_data.get('notes')
                                if invoice_data.get('date'):
                                    existing.date = datetime.fromisoformat(invoice_data['date'])
                                if invoice_data.get('due_date'):
                                    existing.due_date = datetime.fromisoformat(invoice_data['due_date'])
                                updated_count += 1
                            else:
                                # إنشاء فاتورة جديدة
                                new_invoice = Invoice(
                                    invoice_number=invoice_data.get('invoice_number'),
                                    client_id=invoice_data.get('client_id'),
                                    date=datetime.fromisoformat(invoice_data['date']) if invoice_data.get('date') else datetime.now(),
                                    due_date=datetime.fromisoformat(invoice_data['due_date']) if invoice_data.get('due_date') else None,
                                    total_amount=invoice_data.get('total_amount', 0),
                                    paid_amount=invoice_data.get('paid_amount', 0),
                                    status=invoice_data.get('status'),
                                    notes=invoice_data.get('notes')
                                )
                                self.session.add(new_invoice)
                                restored_count += 1

                        self.session.commit()
                        self.refresh_data()  # إعادة تحميل البيانات

                        self.show_success_message(f"تم استعادة النسخة الاحتياطية بنجاح!\n\nتم إضافة {restored_count} فاتورة جديدة\nتم تحديث {updated_count} فاتورة موجودة")
                    else:
                        self.show_error_message("ملف النسخة الاحتياطية غير صالح!")

        except Exception as e:
            self.show_error_message(f"حدث خطأ في استعادة النسخة الاحتياطية: {str(e)}")